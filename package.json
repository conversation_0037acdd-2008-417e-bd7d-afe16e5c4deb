{"name": "silentpalms", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "clean": "echo 'cleaning...' && rm -rf .next && echo 'Done.'"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tooltip": "^1.2.7", "@radix-ui/react-visually-hidden": "^1.2.3", "@tabler/icons-react": "^3.34.0", "@tanstack/react-table": "^8.21.3", "class-variance-authority": "^0.7.1", "cloudinary": "^2.7.0", "clsx": "^2.1.1", "cmdk": "^1.1.1", "country-list": "^2.3.0", "date-fns": "^4.1.0", "framer-motion": "^12.23.12", "lucide-react": "^0.537.0", "motion": "^12.23.12", "next": "15.4.2", "next-themes": "^0.4.6", "radix-ui": "^1.4.2", "react": "19.1.0", "react-day-picker": "^9.8.0", "react-dom": "19.1.0", "react-hook-form": "^7.60.0", "react-slick": "^0.30.3", "slick-carousel": "^1.8.1", "sonner": "^2.0.6", "tailwind-merge": "^3.3.1", "zod": "^4.0.5", "zustand": "^5.0.7"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/country-list": "^2.1.4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/react-slick": "^0.23.13", "eslint": "^9", "eslint-config-next": "15.4.2", "tailwindcss": "^4", "tw-animate-css": "^1.3.5", "typescript": "^5"}, "trustedDependencies": ["@tailwindcss/oxide", "unrs-resolver"]}