import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  images: {
    remotePatterns: [
      {
        protocol: "https",
        hostname: "res.cloudinary.com",
      },
    ],
  },

  typescript: {
    ignoreBuildErrors: true,
  },

  eslint: {
    ignoreDuringBuilds: true,
  },
  allowedDevOrigins: ["local-origin.dev", "*.local-origin.dev", "************"],
  experimental: {
    optimizePackageImports: [
      "react-scroll",
      "react-snap-carousel",
      "framer-motion",
      "use-debounce",
      "next-themes",
      "lucide-react",
      "zustand",
      "slick-carousel",
      "react-slick",
      "country-list",
      "radix-ui",
      "react-day-picker",
      "zod",
    ],
    webpackMemoryOptimizations: true,
    serverSourceMaps: false,
    preloadEntriesOnStart: false,
  },
};

export default nextConfig;
