# Changelog

All notable changes to this project will be documented in this file.

## 2025-08-08

### Added
- Created a `GallerySkeleton.tsx` component to provide a loading state for the gallery.
- Updated the gallery page to use `React.Suspense` and the new skeleton loader, improving the user experience while images are being fetched.

### Changed
- Updated `GallerySkeleton.tsx` to more accurately reflect the masonry grid layout with varying heights for skeleton items and a `max-w-6xl` container to prevent layout shift.

### Fixed
- Corrected an issue in `GallerySkeleton.tsx` where only one skeleton card was being rendered. The component now uses inline styles for dynamic heights to ensure all skeleton cards are displayed correctly.

### Refactored
- Removed redundant `max-w-6xl`, `mx-auto`, and `px-4` classes from `GallerySkeleton.tsx` to rely on the parent container for layout, resulting in cleaner code.
