import Telephone from "../../public/contacticons/phone.png";
import Email from "../../public/contacticons/email.png";
import Whatsapp from "../../public/contacticons/whatsapp.png";
import Facebook from "../../public/contacticons/facebook.png";

import { type StaticImageData } from "next/image";

export interface IContactInfo {
  icon: StaticImageData;
  title: string;
  text?: string;
  link?: string;
}

export const contactInfo: IContactInfo[] = [
  {
    icon: Telephone,
    title: "Telephone",
    text: "Feel free to reach us via telephone for immediate assistance from our team.",
    link: "tel:+254798024710",
  },
  {
    icon: Email,
    title: "Email",
    text: "Send us an email for any inquiries or questions you may have.",
    link: "mailto:<EMAIL>",
  },
  {
    icon: Whatsapp,
    title: "WhatsApp",
    text: "Chat with us directly on WhatsApp for a quick and easy response from us.",
    link: "https://wa.me/+254798024710",
  },
  {
    icon: Facebook,
    title: "Facebook",
    text: "Connect with us on our Facebook page for the latest updates and support.",
    link: "https://www.facebook.com/people/Silent-Palms-Villa-Diani/100076528548033/?paipv=0&eav=AfZ2qpQWze9YsfK4n8GGA2wxyhBYChxAlsA5wsmjTSBJQJvS8Ow2S-9umeKDgu1SG7s&_rdr",
  },
];
