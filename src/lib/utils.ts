import { clsx, type ClassValue } from "clsx";
import { differenceInDays, format } from "date-fns";
import { twMerge } from "tailwind-merge";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export const formatToKsh = (value: number) => {
  return new Intl.NumberFormat("en-KE", {
    style: "currency",
    currency: "KES",
    minimumFractionDigits: 0,
    maximumFractionDigits: 2,
  }).format(value);
};

export const delay = (ms: number) =>
  new Promise((resolve) => setTimeout(resolve, ms));

export const capitalizeFirstLetter = (value: string) => {
  if (!value) {
    return "";
  }
  return value.charAt(0).toUpperCase() + value.slice(1);
};

export const formatDate = (dateValue: string) => {
  if (!dateValue) {
    return;
  }
  const formattedDate = format(new Date(dateValue), "MMMM do, yyyy");
  return formattedDate;
};

export const calculateNights = (startDate: string, endDate: string) => {
  if (!startDate || !endDate) {
    return;
  }
  const arrivalDate = new Date(startDate);
  const departureDate = new Date(endDate);
  const nights = differenceInDays(departureDate, arrivalDate);
  return nights;
};
