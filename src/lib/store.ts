import type { BookingAvailabilityData } from "@/types";
import { create } from "zustand";
import { persist } from "zustand/middleware";

type FormState = {
  currentStep: number;
  formData: BookingAvailabilityData;
  formId?: string;
  setCurrentStep: (step: number) => void;
  setFormData: (data: BookingAvailabilityData) => void;
  setFormId: (id: string) => void;
  resetForm: () => void;
  getLatestState: () => FormState;
};

// helper to get latest state

const getStorageData = () => {
  try {
    const storageData = localStorage.getItem("form-storage");
    if (!storageData) return null;
    const parsedData = JSON.parse(storageData);
    return parsedData.state as FormState;
  } catch (error) {
    console.error("Error reading from localStorage:", error);
    return null;
  }
};

export const useFormStore = create<FormState>()(
  persist(
    (set, get) => ({
      currentStep: 1,
      formData: {},
      setCurrentStep: (step) => set({ currentStep: step }),
      setFormData: (data) =>
        set((state) => ({
          formData: { ...state.formData, ...data },
        })),
      setFormId: (id) => set({ formId: id }),
      resetForm: () => set({ currentStep: 1, formData: {}, formId: undefined }),
      getLatestState: () => getStorageData() || get(),
    }),
    {
      name: "form-storage",
    }
  )
);
