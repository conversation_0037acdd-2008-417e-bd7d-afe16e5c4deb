import type { BookingDetailsData } from "@/types";
import { create } from "zustand";
import { persist } from "zustand/middleware";

type BookingFormState = {
  bookingFormData: BookingDetailsData;
  setBookingData: (data: BookingDetailsData) => void;
  resetBookingData: () => void;
  getLatestBookingState: () => BookingFormState;
};

// helper to get latest state
const getBookingStorageData = () => {
  try {
    const bookingStorageData = localStorage.getItem("booking-form-storage");
    if (!bookingStorageData) return null;
    const parsedData = JSON.parse(bookingStorageData);
    return parsedData.state as BookingFormState;
  } catch (error) {
    console.error("Error reading from localStorage:", error);
    return null;
  }
};

export const useBookingStore = create<BookingFormState>()(
  persist(
    (set, get) => ({
      bookingFormData: {},
      setBookingData: (data) => set({ bookingFormData: data }),
      resetBookingData: () => set({ bookingFormData: {} }),
      getLatestBookingState: () => getBookingStorageData() || get(),
    }),
    {
      name: "booking-form-storage",
    }
  )
);
