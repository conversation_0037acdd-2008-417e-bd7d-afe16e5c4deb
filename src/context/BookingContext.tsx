/* eslint-disable @typescript-eslint/no-explicit-any */

"use client";

import {
  createContext,
  useContext,
  useState,
  useEffect,
  ReactNode,
} from "react";

interface BookingState {
  stepOneData?: any;
  stepThreeData?: any;
  accommodationId?: string;
}

interface BookingContextType {
  bookingState: BookingState;
  setStepOneData: (data: any) => void;
  setStepThreeData: (data: any) => void;
  setAccommodationId: (id: string) => void;
  resetBookingState: () => void;
}

const BookingContext = createContext<BookingContextType | undefined>(undefined);

export const BookingProvider = ({ children }: { children: ReactNode }) => {
  const [bookingState, setBookingState] = useState<BookingState>(() => {
    if (typeof window !== "undefined") {
      const savedState = localStorage.getItem("bookingState");
      return savedState ? JSON.parse(savedState) : {};
    }
    return {};
  });

  useEffect(() => {
    localStorage.setItem("bookingState", JSON.stringify(bookingState));
  }, [bookingState]);

  const setStepOneData = (data: any) => {
    setBookingState((prevState) => ({ ...prevState, stepOneData: data }));
  };

  const setStepThreeData = (data: any) => {
    setBookingState((prevState) => ({ ...prevState, stepThreeData: data }));
  };

  const setAccommodationId = (id: string) => {
    setBookingState((prevState) => ({ ...prevState, accommodationId: id }));
  };

  const resetBookingState = () => {
    setBookingState({});
  };

  return (
    <BookingContext.Provider
      value={{
        bookingState,
        setStepOneData,
        setStepThreeData,
        setAccommodationId,
        resetBookingState,
      }}
    >
      {children}
    </BookingContext.Provider>
  );
};

export const useBooking = () => {
  const context = useContext(BookingContext);
  if (context === undefined) {
    throw new Error("useBooking must be used within a BookingProvider");
  }
  return context;
};
