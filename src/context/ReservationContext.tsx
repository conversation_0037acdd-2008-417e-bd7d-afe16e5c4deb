"use client";
import {
  createContext,
  useContext,
  useState,
  ReactNode,
  useEffect,
} from "react";

interface ReservationData {
  startDate: string;
  endDate: string;
  country: string;
  promoCode?: string;
}

interface ReservationContextType {
  reservationData: ReservationData | null;
  setReservationData: (data: ReservationData) => void;
}

const ReservationContext = createContext<ReservationContextType | undefined>(
  undefined,
);

export const ReservationProvider = ({ children }: { children: ReactNode }) => {
  const [reservationData, setReservationDataState] =
    useState<ReservationData | null>(null);

  useEffect(() => {
    const storedData = localStorage.getItem("reservationData");
    if (storedData) {
      setReservationDataState(JSON.parse(storedData));
    }
  }, []);

  const setReservationData = (data: ReservationData) => {
    setReservationDataState(data);
    localStorage.setItem("reservationData", JSON.stringify(data));
  };

  return (
    <ReservationContext.Provider
      value={{ reservationData, setReservationData }}
    >
      {children}
    </ReservationContext.Provider>
  );
};

export const useReservation = () => {
  const context = useContext(ReservationContext);
  if (context === undefined) {
    throw new Error("useReservation must be used within a ReservationProvider");
  }
  return context;
};
