"use client";
import {
  createContext,
  useContext,
  useState,
  ReactNode,
  useEffect,
} from "react";

interface IHeroContext {
  allImagesLoaded: boolean;
  setAllImagesLoaded: (loaded: boolean) => void;
}

const HeroContext = createContext<IHeroContext | undefined>(undefined);

export const HeroProvider = ({ children }: { children: ReactNode }) => {
  const [allImagesLoaded, setAllImagesLoaded] = useState(false);

  return (
    <HeroContext.Provider value={{ allImagesLoaded, setAllImagesLoaded }}>
      {children}
    </HeroContext.Provider>
  );
};

export const useHero = () => {
  const context = useContext(HeroContext);
  if (context === undefined) {
    throw new Error("useHero must be used within a HeroProvider");
  }
  return context;
};