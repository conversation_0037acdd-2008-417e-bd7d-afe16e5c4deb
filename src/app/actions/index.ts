"use server";

import { type BookingDetails, type PendingBookingDetails } from "@/types";
import { cache } from "react";

interface DataAvailability {
  success: boolean;
  message: string;
  error?: string;
}

const API_URL = process.env.NEXT_PUBLIC_API_URL;

export const getCategories = cache(async () => {
  try {
    const response = await fetch(`${API_URL}/api/categories`, {
      cache: "force-cache",
      next: { tags: ["categories"] },
    });
    if (!response.ok) {
      return {
        success: false,
        message: "An error occured on the server",
      };
    }
    const categories = await response.json();
    if (!categories || categories.length <= 0) {
      return {
        success: false,
        message: "No categories found",
      };
    }
    return {
      success: true,
      data: categories,
    };
  } catch (error) {
    console.error(`Internal server error: ${error}`);
    return {
      success: false,
      message: `Internal server error`,
    };
  }
});

export const getCategoryAccommodations = cache(async (name: string) => {
  if (!name) {
    return {
      success: false,
      message: "Please provide a category name",
    };
  }
  try {
    const response = await fetch(
      `${API_URL}/api/categories/${name}/get-accommodations`,
      {
        cache: "force-cache",
        next: { tags: ["category-accommodtions"] },
      },
    );
    if (!response.ok) {
      return {
        success: false,
        message: "An error occured on the server",
      };
    }
    const categoryData = await response.json();
    if (!categoryData) {
      return {
        success: false,
        message: "No category data found",
      };
    }
    return {
      success: true,
      data: categoryData,
    };
  } catch (error) {
    console.error(`Internal server error: ${error}`);
    return {
      success: false,
      message: `Internal server error`,
    };
  }
});

export const getGalleryImages = cache(async () => {
  try {
    const response = await fetch(`${API_URL}/api/gallery`, {
      cache: "force-cache",
      next: { tags: ["gallery"] },
    });
    if (!response.ok) {
      return {
        success: false,
        message: "An error occured on the server",
      };
    }
    const images = await response.json();
    if (!images || images.length <= 0) {
      return {
        success: false,
        message: "No images found",
      };
    }
    return {
      success: true,
      data: images,
    };
  } catch (error) {
    console.error(`Internal server error: ${error}`);
    return {
      success: false,
      message: `Internal server error`,
    };
  }
});

export const confirmAvailability = cache(
  async (id: string, startDate: string, endDate: string) => {
    if (!id || !startDate || !endDate) {
      return {
        success: false,
        message: "Missing fields",
      };
    }
    try {
      const response = await fetch(
        `${API_URL}/api/accommodations/${id}/availability?startDate=${startDate}&endDate=${endDate}`,
      );
      if (!response.ok) {
        return {
          success: false,
          message: "An error occured on the server",
        };
      }
      const data: DataAvailability = await response.json();
      if (!data.success) {
        return {
          success: false,
          message: data.message,
        };
      }
      return {
        success: true,
        message: data.message,
      };
    } catch (error) {
      console.error(`Internal server error: ${error}`);
      return {
        success: false,
        message: `Internal server error`,
      };
    }
  },
);

export const createPendingBooking = async (bookingData: BookingDetails) => {
  try {
    const response = await fetch(`${API_URL}/api/bookings`, {
      method: "POST",
      body: JSON.stringify(bookingData),
    });

    if (!response.ok) {
      return {
        success: false,
        message: `Internal server error`,
      };
    }
    const data = await response.json();
    if (!data.success) {
      return {
        success: false,
        message: data.message || "Failed to create booking",
        error: data.error || "Unknown error",
      };
    }
    return {
      success: true,
      booking: data.data,
      message: data.message || "Booking created successfully",
    };
  } catch (error) {
    console.error(`Error creating pending booking: ${error}`);
    return {
      success: false,
      message: `Internal server error while booking`,
      error: error instanceof Error ? error.message : "Internal server error",
    };
  }
};

export const confirmDeposit = async (data: PendingBookingDetails) => {
  if (!data) {
    return {
      success: false,
      message: `No data provided`,
    };
  }
  try {
    const { depositAmount, status, bookingId } = data;
    if (!depositAmount || !status || !bookingId) {
      return {
        success: false,
        message: `Invalid parameters while making payment`,
      };
    }

    const payload = {
      status,
      depositAmount,
    };

    const response = await fetch(
      `${API_URL}/api/bookings/${bookingId}/confirm-deposit`,
      {
        method: "PATCH",
        body: JSON.stringify(payload),
      },
    );

    if (!response.ok) {
      return {
        success: false,
        message: `Invalid response from server`,
      };
    }
    return {
      success: true,
      message: `Booking made successfully`,
    };
  } catch (error) {
    console.error(`Internal server error:${error}`);
    return {
      success: false,
      message: "Internal server error",
    };
  }
};
