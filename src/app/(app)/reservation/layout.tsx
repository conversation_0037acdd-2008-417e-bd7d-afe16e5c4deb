import React from "react";
import StepNavigation from "@/components/StepNavigation";
import { BookingProvider } from "@/context/BookingContext";

export default function ReservationLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <div className="w-full px-4">
      <div className="mt-16 mb-28 py-10 flex flex-col  text-black">
        <BookingProvider>
          <StepNavigation />
          <div className="w-full">{children}</div>
        </BookingProvider>
      </div>
    </div>
  );
}
