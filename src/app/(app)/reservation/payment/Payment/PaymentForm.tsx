import { anton } from "@/app/fonts";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { formatToKsh } from "@/lib/utils";
import type { PendingBookingDetails } from "@/types";

interface PaymentFormProps {
  depositAmount: number;
  bookingId: string;
  status: string;
  isSubmitting: boolean;
  onSubmit: (data: PendingBookingDetails) => void;
}

const PaymentForm = ({
  depositAmount,
  bookingId,
  status,
  isSubmitting,
  onSubmit,
}: PaymentFormProps) => {
  const handleSubmit = () => {
    const paymentDetails = {
      depositAmount,
      bookingId,
      status,
    };
    onSubmit(paymentDetails as PendingBookingDetails);
  };

  return (
    <div>
      <div>
        <h2 className={`${anton.className} text-2xl font-bold mb-6`}>
          Payment With Credit Card
        </h2>
        <div className="space-y-6">
          <div className="flex flex-col gap-1">
            <Label htmlFor="cardNumber">Card Number</Label>
            <Input
              className="py-6 rounded-none outline-none"
              id="cardNumber"
              placeholder="**** **** **** ****"
            />
          </div>
          <div className="grid grid-cols-2 gap-4">
            <div className="flex flex-col gap-1">
              <Label htmlFor="expiryDate">Expiry Date</Label>
              <Input
                className="py-6 rounded-none outline-none"
                id="expiryDate"
                placeholder="MM/YY"
              />
            </div>
            <div className="flex flex-col gap-1">
              <Label htmlFor="cvv">CVV</Label>
              <Input
                className="py-6 rounded-none outline-none"
                id="cvv"
                placeholder="***"
              />
            </div>
          </div>
          <div className="flex flex-col gap-1">
            <Label htmlFor="cardHolder">Card Holder Name</Label>
            <Input
              className="py-6 rounded-none outline-none"
              id="cardHolder"
              placeholder="John Doe"
            />
          </div>
          <Button
            onClick={handleSubmit}
            disabled={isSubmitting}
            className={`${anton.className} uppercase text-lg w-full rounded-none py-6 cursor-pointer`}
          >
            Pay {formatToKsh(depositAmount)} Now
          </Button>
        </div>
      </div>
    </div>
  );
};

export default PaymentForm;
