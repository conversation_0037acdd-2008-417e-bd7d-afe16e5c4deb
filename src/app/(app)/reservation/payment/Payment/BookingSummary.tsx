import { format } from "date-fns";

// eslint-disable-next-line @typescript-eslint/no-explicit-any
const BookingSummary = ({ bookingState }: { bookingState: any }) => {
  return (
    <div className="bg-gray-50 p-6 rounded-lg">
      <h2 className="text-2xl font-bold mb-4">Booking Summary</h2>
      <div className="space-y-4">
        <div>
          <h3 className="text-lg font-semibold">
            {bookingState.accommodationId}
          </h3>
        </div>
        <div>
          <h3 className="text-lg font-semibold">Dates</h3>
          <p className="text-gray-600">
            Check-in:{" "}
            {bookingState.stepOneData?.checkin
              ? format(new Date(bookingState.stepOneData.checkin), "PPP")
              : "N/A"}
          </p>
          <p className="text-gray-600">
            Check-out:{" "}
            {bookingState.stepOneData?.checkout
              ? format(new Date(bookingState.stepOneData.checkout), "PPP")
              : "N/A"}
          </p>
        </div>
        <div>
          <h3 className="text-lg font-semibold">Guests</h3>
          <p className="text-gray-600">
            {bookingState.stepOneData?.guests} Adults
          </p>
          <p className="text-gray-600">
            {bookingState.stepOneData?.bedrooms} Bedrooms
          </p>
        </div>
        <div>
          <h3 className="text-lg font-semibold">Guest Information</h3>
          <p className="text-gray-600">
            {bookingState.stepThreeData?.firstName}{" "}
            {bookingState.stepThreeData?.lastName}
          </p>
          <p className="text-gray-600">{bookingState.stepThreeData?.email}</p>
          <p className="text-gray-600">
            {bookingState.stepThreeData?.phoneNumber}
          </p>
          <p className="text-gray-600">{bookingState.stepThreeData?.country}</p>
        </div>
      </div>
    </div>
  );
};

export default BookingSummary;
