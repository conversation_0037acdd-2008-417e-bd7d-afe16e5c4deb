"use client";

import { Input } from "@/components/ui/input";
import { calculateNights, formatDate, formatToKsh } from "@/lib/utils";
import { PendingBooking } from "@/types";
import { ChevronDown } from "lucide-react";
import { useEffect, useState } from "react";

const RefinedBookingSummary = () => {
  const [value, setValue] = useState<string>("");
  const [bookingSummary, setBookingSummary] =
    useState<PendingBooking["redactedBooking"]>();
  const [accommodation, setAccommodation] =
    useState<PendingBooking["accommodation"]>();

  useEffect(() => {
    const storedData = localStorage.getItem("pendingBooking");
    if (storedData) {
      const parsedData: PendingBooking = JSON.parse(storedData);
      if (parsedData) {
        setBookingSummary(parsedData.redactedBooking);
        setAccommodation(parsedData.accommodation);
      }
    }
  }, []);

  if (!bookingSummary) {
    return <p>Error getting summary</p>;
  }

  return (
    <div className="border-[0.8px] border-primary/55">
      <div className=" flex items-center justify-between px-2 py-3 border-b-[0.8px] border-primary/45">
        <div className="flex items-center gap-0.5">
          <p className="font-medium text-lg">Stay Details: </p>
          <p>{formatToKsh(bookingSummary.totalAmount)}</p>
        </div>
        <div className="flex items-center gap-0.5">
          <p>Hide</p>
          <ChevronDown size={16} />
        </div>
      </div>
      <div className="border-b-[0.8px] border-primary/45 pb-8 flex flex-col gap-3 mb-5">
        <div className="px-2 py-3 flex flex-col gap-2 text-[15px]">
          <div className="flex items-center justify-between">
            <p>Arrive:</p>
            <p>{formatDate(bookingSummary.startDate)}</p>
          </div>
          <div className="flex items-center justify-between">
            <p>Depart:</p>
            <p>{formatDate(bookingSummary.endDate)}</p>
          </div>
          <div className="flex items-center justify-between">
            <p>Nights:</p>
            <p>
              {calculateNights(
                bookingSummary.startDate,
                bookingSummary.endDate
              )}
            </p>
          </div>
          <div className="flex items-center justify-between">
            <p>Guests:</p>
            <p>{bookingSummary.numberOfGuests}</p>
          </div>
          {accommodation && (
            <div className="flex items-center justify-between">
              <p>Accommodation:</p>
              <p>{accommodation.name}</p>
            </div>
          )}
          <div className="flex items-center justify-between">
            <p>Bedrooms:</p>
            <p>2</p>
          </div>
          <div className="flex items-center justify-between">
            <p>Policy:</p>
            <p className="underline">See Details</p>
          </div>
        </div>
        <div className="w-full flex items-center justify-between px-2">
          <p className="text-[15px]">Coupon Code</p>
          <Input
            type="text"
            value={value}
            onChange={(e) => setValue(e.target.value)}
            placeholder="Enter Promo Code"
            className="rounded-none outline-none py-2 w-[180px] border-[0.8px] border-primary/30"
          />
        </div>
      </div>
      <div className="px-2 py-3 flex flex-col gap-2 text-lg font-medium">
        <div className="flex items-center justify-between">
          <p>Grand Total:</p>
          <p className="font-normal">
            {formatToKsh(bookingSummary.totalAmount)}
          </p>
        </div>
        <div className="flex items-center justify-between">
          <p>Due Now:</p>
          <p className="font-normal">
            {formatToKsh(bookingSummary.depositAmount)}
          </p>
        </div>
      </div>
    </div>
  );
};

export default RefinedBookingSummary;
