/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";

import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { useBooking } from "@/context/BookingContext";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { toast } from "sonner";
import { allCountryNames } from "@/data/countries";
import { Combobox } from "@/components/ui/combo-box";
import { useRouter } from "next/navigation";
import { BookingRoutes } from "@/types";
import { format } from "date-fns";
import { createPendingBooking } from "@/app/actions";
import { useState } from "react";
import LoadingModal from "@/components/Loaders/LoadingModal";

const FormSchema = z.object({
  firstName: z.string().min(2, {
    error: "First name must be at least 2 characters.",
  }),
  lastName: z.string().min(2, {
    error: "Last name must be at least 2 characters.",
  }),
  email: z.email({
    error: "Please enter a valid email address.",
  }),
  phoneNumber: z.string().min(10, {
    error: "Phone number must be at least 10 digits.",
  }),
  country: z.string({
    error: "Please select a country.",
  }),
});

export function GuestInfoForm() {
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();
  const { setStepThreeData, bookingState } = useBooking();
  const form = useForm<z.infer<typeof FormSchema>>({
    resolver: zodResolver(FormSchema),
    defaultValues: {
      firstName: "",
      lastName: "",
      email: "",
      phoneNumber: "",
      country: "",
    },
  });

  async function onSubmit(data: z.infer<typeof FormSchema>) {
    let bookingDetails: any = {};
    if (!bookingState || !bookingState.accommodationId) {
      return;
    }
    setIsLoading(true);
    try {
      const startDate = format(
        new Date(bookingState.stepOneData.checkin),
        "yyyy-MM-dd"
      );
      const endDate = format(
        new Date(bookingState.stepOneData.checkout),
        "yyyy-MM-dd"
      );

      bookingDetails = {
        accommodationId: bookingState.accommodationId,
        guestFirstName: data.firstName,
        guestLastName: data.lastName,
        guestEmail: data.email,
        guestCountry: data.country,
        guestPhoneNumber: data.phoneNumber,
        startDate,
        endDate,
        numberOfGuests: bookingState.stepOneData.guests,
      };

      const bookingData = await createPendingBooking(bookingDetails);
      if (!bookingData.success) {
        toast.error(`An error occurred while booking`);
        return;
      }
      localStorage.setItem(
        "pendingBooking",
        JSON.stringify(bookingData.booking)
      );
      setStepThreeData(data);
      router.push(`${BookingRoutes.PAYMENT_ROUTE}?completed=0,1,2`);
    } catch (error) {
      console.error(`Error getting payment details: ${error}`);
      toast.error("Error", {
        description: "Error getting payment details",
      });
    } finally {
      setIsLoading(false);
    }
  }

  return (
    <>
      <LoadingModal
        isOpen={isLoading}
        message="Creating Payment Details"
        text="Generating details for your stay..."
      />
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <FormField
              control={form.control}
              name="firstName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>First Name</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="e.g. John"
                      {...field}
                      className="py-6 rounded-none"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="lastName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Last Name</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="e.g. Doe"
                      {...field}
                      className="py-6 rounded-none"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <FormField
              control={form.control}
              name="phoneNumber"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Phone Number</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="e.g. +1234567890"
                      {...field}
                      className="py-6 rounded-none"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="country"
              render={({ field }) => (
                <FormItem className="flex flex-col">
                  <FormLabel>Country</FormLabel>
                  <Combobox
                    options={allCountryNames.map((country) => ({
                      value: country,
                      label: country,
                    }))}
                    value={field.value}
                    onChange={field.onChange}
                    placeholder="Select country..."
                    searchPlaceholder="Search country..."
                    notFoundText="No country found."
                  />
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
          <FormField
            control={form.control}
            name="email"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Email</FormLabel>
                <FormControl>
                  <Input
                    placeholder="e.g. <EMAIL>"
                    {...field}
                    className="py-6 rounded-none"
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <Button type="submit" className="w-full py-6 rounded-none">
            Next
          </Button>
        </form>
      </Form>
    </>
  );
}
