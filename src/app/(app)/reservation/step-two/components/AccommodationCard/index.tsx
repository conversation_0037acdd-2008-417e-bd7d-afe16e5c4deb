import { anton } from "@/app/fonts";
import { Button } from "@/components/ui/button";
import { formatToKsh } from "@/lib/utils";
import Image from "next/image";

interface IAccommodationCardProps {
  id: string;
  name: string;
  summary: string;
  bedrooms: number;
  pricePerNight: number;
  images: { url: string }[];
  onSelect: (id: string) => void;
}

const AccommodationCard = ({
  id,
  name,
  summary,
  pricePerNight,
  bedrooms,
  images,
  onSelect,
}: IAccommodationCardProps) => {
  return (
    <div className="flex flex-col gap-4 bg-white w-full shadow-sm p-4 rounded-md cursor-pointer">
      <div className="flex items-start flex-col gap-[2px]">
        <h4 className={`${anton.className} leading-none font-bold text-lg`}>
          {formatToKsh(pricePerNight)}
        </h4>
        <p className="text-sm text-neutral-500">{bedrooms} bedroom unit</p>
      </div>
      <div className="h-[160px] w-full relative rounded-md flex items-center justify-center text-white text-2xl font-bold">
        <Image
          src={images[0].url}
          alt="cover"
          fill
          className="object-cover rounded-md"
          quality={100}
          priority={true}
          loading="eager"
        />
      </div>
      <div className="flex flex-col gap-3 items-start">
        <h4
          className={`${anton.className} leading-none font-semibold text-2xl`}
        >
          {name}
        </h4>
        <p className="text-sm/4 text-neutral-800 line-clamp-2">{summary}</p>
      </div>
      <div>
        <Button
          type="button"
          size={"default"}
          className="bg-primary rounded-full px-8 cursor-pointer font-bold"
          onClick={() => onSelect(id)}
        >
          Select
        </Button>
      </div>
    </div>
  );
};

export default AccommodationCard;
