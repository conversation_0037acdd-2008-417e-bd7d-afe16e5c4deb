"use client";

import ResevationHeader from "@/components/ReservationHeader";
import AccommodationCard from "./components/AccommodationCard";
import { useBooking } from "@/context/BookingContext";
import { useRouter } from "next/navigation";
import { BookingRoutes, IAccommodation } from "@/types";
import { useEffect, useState } from "react";

const StepTwoPage = () => {
  const router = useRouter();
  const { setAccommodationId } = useBooking();
  const [availableAccommodations, setAvailableAccommodations] = useState<
    IAccommodation[]
  >([]);

  useEffect(() => {
    const storedData = localStorage.getItem("availableAccommodations");
    if (storedData) {
      setAvailableAccommodations(JSON.parse(storedData));
    }
  }, []);

  const handleSelectAccommodation = (id: string) => {
    setAccommodationId(id);
    router.push(`${BookingRoutes.GUEST_INFO}?completed=0,1`);
  };

  return (
    <div>
      <ResevationHeader title="Choose Your Accommodation" />
      <div className="max-w-6xl mx-auto p-4">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {availableAccommodations.map((accommodation) => (
            <AccommodationCard
              key={accommodation.id}
              id={accommodation.id}
              name={accommodation.name}
              summary={accommodation.summary}
              bedrooms={accommodation.bedrooms}
              pricePerNight={accommodation.pricePerNight}
              images={accommodation.images}
              onSelect={handleSelectAccommodation}
            />
          ))}
        </div>
      </div>
    </div>
  );
};

export default StepTwoPage;
