"use server";

import { delay } from "@/lib/utils";

const API_URL = process.env.NEXT_PUBLIC_API_URL;

export const getAvailableAccommodations = async (
  startDate: string,
  endDate: string,
  guests: number,
  bedrooms: number,
) => {
  if (!startDate || !endDate || !guests || !guests) {
    return {
      success: false,
      message: `Please fill all fields to continue`,
    };
  }
  try {
    const response = await fetch(
      `${API_URL}/api/accommodations/available-accommodations?startDate=${startDate}&endDate=${endDate}&guests=${guests}&bedrooms=${bedrooms}`,
    );
    if (!response.ok) {
      return {
        success: false,
        message: `Internal server error`,
      };
    }
    const accommodations = await response.json();
    if (!accommodations || accommodations.length <= 0) {
      return {
        success: false,
        message: `No accommodations for those dates`,
      };
    }

    await delay(500);

    return {
      success: true,
      data: accommodations,
    };
  } catch (error) {
    console.error("Failed to fetch accommodations:", error);
    return {
      success: false,
      message: `Internal server error`,
    };
  }
};
