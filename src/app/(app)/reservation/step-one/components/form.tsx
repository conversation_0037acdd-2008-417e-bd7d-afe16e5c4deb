"use client";

import { zodResolver } from "@hookform/resolvers/zod";
import { format } from "date-fns";
import { CalendarIcon, BedDouble, Users } from "lucide-react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import { z } from "zod";
import { useRouter } from "next/navigation";
import { BookingRoutes } from "@/types";
import { useState } from "react";
import LoadingModal from "@/components/Loaders/LoadingModal";

import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Input } from "@/components/ui/input";
import { useBooking } from "@/context/BookingContext";
import { getAvailableAccommodations } from "../actions";

const FormSchema = z
  .object({
    checkin: z.date({
      error: "A check-in date is required.",
    }),
    checkout: z.date({
      error: "A check-out date is required.",
    }),
    guests: z.coerce
      .number<number>()
      .min(1, "At least one guest is required.")
      .max(6, { error: "Maximum number of guests is 6" }),
    bedrooms: z.coerce
      .number<number>()
      .min(1, { error: "At least one bedroom is required." })
      .max(2, { error: "Maximum number is 2 bedrooms" }),
  })
  .refine((data) => data.checkout > data.checkin, {
    message: "Check-out date must be after check-in date.",
    path: ["checkout"],
  })
  .refine((data) => data.checkin < data.checkout, {
    message: "Check-in date must be before check-out date",
    path: ["checkin"],
  });

export function CalendarForm() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const { setStepOneData } = useBooking();
  const form = useForm<z.infer<typeof FormSchema>>({
    resolver: zodResolver(FormSchema),
    defaultValues: {
      guests: 1,
      bedrooms: 2,
    },
  });

  async function onSubmit(data: z.infer<typeof FormSchema>) {
    setIsLoading(true);
    try {
      setStepOneData(data);
      const startDate = format(data.checkin, "yyyy-MM-dd");
      const endDate = format(data.checkout, "yyyy-MM-dd");
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      let accommodationsData: any = [];
      const response = await getAvailableAccommodations(
        startDate,
        endDate,
        data.guests,
        data.bedrooms
      );

      if (!response.success) {
        toast.error("Error", {
          description: response.message,
        });
        return;
      }

      accommodationsData = response.data;

      // Store accommodationsData in local storage or context if it's too large for URL params
      localStorage.setItem(
        "availableAccommodations",
        JSON.stringify(accommodationsData)
      );

      router.push(`${BookingRoutes.ACCOMODATION_SELECTION}?completed=0`);
    } catch (error) {
      console.error("Failed to fetch accommodations:", error);
      toast.error("Error", {
        description:
          "Failed to fetch available accommodations. Please try again.",
      });
    } finally {
      setIsLoading(false);
    }
  }

  return (
    <>
      <LoadingModal
        isOpen={isLoading}
        message="Fetching Accommodations"
        text="Finding available accommodations..."
      />
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <FormField
              control={form.control}
              name="checkin"
              render={({ field }) => (
                <FormItem className="flex flex-col">
                  <FormLabel>Check In</FormLabel>
                  <Popover>
                    <PopoverTrigger asChild>
                      <FormControl>
                        <Button
                          variant={"outline"}
                          className={cn(
                            "w-full pl-3 text-left font-normal rounded-none py-6",
                            !field.value && "text-muted-foreground"
                          )}
                        >
                          {field.value ? (
                            format(field.value, "PPP")
                          ) : (
                            <span>Pick a date</span>
                          )}
                          <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                        </Button>
                      </FormControl>
                    </PopoverTrigger>
                    <PopoverContent className="w-[364px] p-0" align="start">
                      <Calendar
                        className="w-[364px]"
                        mode="single"
                        selected={field.value}
                        onSelect={field.onChange}
                        disabled={(date) =>
                          date < new Date() ||
                          (form.getValues("checkout") &&
                            date > form.getValues("checkout"))
                        }
                        autoFocus
                      />
                    </PopoverContent>
                  </Popover>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="checkout"
              render={({ field }) => (
                <FormItem className="flex flex-col">
                  <FormLabel>Check Out</FormLabel>
                  <Popover>
                    <PopoverTrigger asChild>
                      <FormControl>
                        <Button
                          variant={"outline"}
                          className={cn(
                            "w-full pl-3 text-left font-normal rounded-none py-6",
                            !field.value && "text-muted-foreground"
                          )}
                        >
                          {field.value ? (
                            format(field.value, "PPP")
                          ) : (
                            <span>Pick a date</span>
                          )}
                          <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                        </Button>
                      </FormControl>
                    </PopoverTrigger>
                    <PopoverContent className="w-[364px] p-0" align="start">
                      <Calendar
                        className="w-[364px]"
                        mode="single"
                        selected={field.value}
                        onSelect={field.onChange}
                        disabled={(date) =>
                          date < (form.getValues("checkin") || new Date())
                        }
                        autoFocus
                      />
                    </PopoverContent>
                  </Popover>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <FormField
              control={form.control}
              name="guests"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Number of Guests</FormLabel>
                  <FormControl>
                    <div className="relative">
                      <Users className="absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-muted-foreground" />
                      <Input
                        type="number"
                        placeholder="e.g. 2"
                        {...field}
                        className="pl-10 py-6 rounded-none"
                        onChange={(e) =>
                          field.onChange(parseInt(e.target.value, 10))
                        }
                      />
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="bedrooms"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Number of Bedrooms</FormLabel>
                  <FormControl>
                    <div className="relative">
                      <BedDouble className="absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-muted-foreground" />
                      <Input
                        type="number"
                        placeholder="e.g. 1"
                        {...field}
                        className="pl-10 py-6 rounded-none"
                        onChange={(e) =>
                          field.onChange(parseInt(e.target.value, 10))
                        }
                      />
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
          <Button
            type="submit"
            className="w-full py-6 rounded-none cursor-pointer"
          >
            Next
          </Button>
        </form>
      </Form>
    </>
  );
}
