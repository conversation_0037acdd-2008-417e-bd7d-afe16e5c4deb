import { Hero<PERSON><PERSON>ider } from "@/context/HeroContext";
import Navbar from "@/components/Navbar";
import { BookingProvider } from "@/context/BookingContext";

export default function AppLayout({ children }: { children: React.ReactNode }) {
  return (
    <BookingProvider>
      <HeroProvider>
        <main>
          <Navbar />
          {children}
        </main>
      </HeroProvider>
    </BookingProvider>
  );
}
