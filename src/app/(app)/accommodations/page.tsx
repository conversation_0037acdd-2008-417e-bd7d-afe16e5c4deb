import { getCategories } from "@/app/actions";
import { GridBackground } from "@/components/grid-background";
import PageHeader from "@/components/PageHeader";

import { Categories } from "@/types";

const AccommodationPage = async () => {
  const categories = await getCategories();

  if (!categories || !categories.data) {
    return (
      <div className="h-screen flex items-center justify-center">
        <p>Category Data Not found</p>
      </div>
    );
  }

  const categoryData: Categories[] = categories.data;

  return (
    <div className="relative flex flex-col min-h-screen w-full">
      <GridBackground className="pt-12 pb-1 bg-background">
        <PageHeader
          title="Our Accommodations"
          text=" Savor the coastal experience in Diani's finest area, Silent Palms Villa, choosing from our comfortable villa accommodations."
        />
      </GridBackground>
      {categoryData && (
        <div className="flex-grow items-center justify-center grid grid-cols-3 gap-2 p-4">
          {categoryData.map((cat) => (
            <div className="border aspect-square" key={cat.id}>
              <p>{cat.name}</p>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default AccommodationPage;
