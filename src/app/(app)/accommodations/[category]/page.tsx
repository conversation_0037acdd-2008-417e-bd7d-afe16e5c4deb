import PageHeader from "@/components/PageHeader";
import { capitalizeFirstLetter } from "@/lib/utils";
import { getCategoryAccommodations } from "@/app/actions";

import CategoryAccommodation from "@/components/CategoryAccommodation";

interface AccommodationImage {
  url: string;
}

interface Accommodation {
  id: string;
  name: string;
  pricePerNight: number;
  summary: string;
  images: AccommodationImage[];
  bedrooms: number;
  maxCapacity: number;
}

const CategoryPage = async ({
  params,
}: {
  params: Promise<{
    category: string;
  }>;
}) => {
  const { category } = await params;

  const CapitalizedCategory = capitalizeFirstLetter(category);
  const result = await getCategoryAccommodations(CapitalizedCategory);

  if (!result.success) {
    return;
  }

  const accommodations: Accommodation[] = result.data.accommodations;
  const categoryName = result.data.categoryName;

  return (
    <div className="px-4 pt-14">
      <PageHeader title={categoryName} />
      {accommodations ? (
        <div className=" mx-auto gap-2 flex">
          {accommodations.map((acc: Accommodation) => (
            <CategoryAccommodation
              key={acc.id}
              id={acc.id}
              name={acc.name}
              pricePerNight={acc.pricePerNight}
              summary={acc.summary}
              bedrooms={acc.bedrooms}
              maxCapacity={acc.maxCapacity}
              images={acc.images}
              category={category}
            />
          ))}
        </div>
      ) : (
        <div>
          <p>No accommodations found</p>
        </div>
      )}
    </div>
  );
};

export default CategoryPage;
