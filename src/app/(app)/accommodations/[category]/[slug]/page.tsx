import { getCategoryAccommodations } from "@/app/actions";
import { anton } from "@/app/fonts";
import CheckAvailabilityButton from "@/components/AvailabilityChecker/CheckAvailabilityButton";
import BackButton from "@/components/BackButton";
import RoomPhotos from "@/components/RoomPhotos";
import { capitalizeFirstLetter, formatToKsh } from "@/lib/utils";
import { BedDouble, Users, Waves } from "lucide-react";
import Image from "next/image";

interface AccommodationImage {
  url: string;
}

interface Accommodation {
  id: string;
  name: string;
  pricePerNight: number;
  summary: string;
  images: AccommodationImage[];
  bedrooms: number;
  maxCapacity: number;
  description: string;
}

const AccommodationPage = async ({
  params,
}: {
  params: Promise<{
    slug: string;
    category: string;
  }>;
}) => {
  const { slug, category } = await params;

  const CapitalizedCategory = capitalizeFirstLetter(category);
  const result = await getCategoryAccommodations(CapitalizedCategory);
  if (!result.success) {
    return;
  }

  const accommodations: Accommodation[] = result.data.accommodations;

  const accommodation = accommodations.find((acc) => acc.name === slug);
  if (!accommodation) {
    return;
  }

  return (
    <div className="px-4 py-[100px] max-w-6xl mx-auto flex flex-col gap-12">
      <div>
        <p className="capitalize text-sm font-medium text-primary/95 mb-3.5">
          {category} accommodations
        </p>
        <h4 className={`${anton.className} text-2xl`}>
          <span className="capitalize">{accommodation.name} - </span>{" "}
          Accommodation with {accommodation.bedrooms} beds
        </h4>
        <div className="py-4 flex items-center justify-between">
          <h5>
            <span className="text-xl leading-[27px] font-semibold">
              {formatToKsh(accommodation.pricePerNight)}
            </span>
            <span className="font-light text-xs">/</span>
            <span className="text-xs">per night</span>
          </h5>
          <CheckAvailabilityButton accommodationId={accommodation.id} />
        </div>
        <div className="relative aspect-video mb-3.5 rounded">
          <Image
            src={accommodation.images[0].url}
            fill
            className="object-cover rounded"
            alt="cover"
          />
        </div>
        <div className="flex items-center flex-wrap gap-2 mb-6">
          <div className="h-10 flex items-center px-4 gap-2 bg-primary/5 rounded-full py-2">
            <Users className="w-6 h-6 text-primary/80" />
            <p> For 1-{accommodation.maxCapacity} guests</p>
          </div>
          <div className="h-10 flex items-center px-4 gap-2 bg-primary/5 rounded-full py-2">
            <BedDouble className="w-6 h-6 text-primary/80" />
            <p>{accommodation.bedrooms} bedrooms with beds</p>
          </div>
          <div className="h-10 flex items-center px-4 gap-2 bg-primary/5 rounded-full py-2">
            <Waves className="w-6 h-6 text-primary/80" />
            <p>Ocean View</p>
          </div>
        </div>
        <div className="w-full">
          <p className="text-[13.5px] text-wrap font-light">
            {accommodation.description}
          </p>
        </div>
      </div>
      <RoomPhotos images={accommodation.images.slice(1)} />
      <div className="flex items-center justify-center w-full -mt-6">
        <BackButton />
      </div>
    </div>
  );
};

export default AccommodationPage;
