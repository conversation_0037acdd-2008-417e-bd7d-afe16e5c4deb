import { getGalleryImages } from "@/app/actions";
import GalleryComponent from "@/components/GalleryComponent";
import { GridBackground } from "@/components/grid-background";
import GallerySkeleton from "@/components/Loaders/GallerySkeleton";
import PageHeader from "@/components/PageHeader";
import { Suspense } from "react";

const GalleryPage = async () => {
  const result = await getGalleryImages();

  if (!result || !result.data) {
    return (
      <div className="h-screen flex items-center justify-center">
        <p>Gallery images not found</p>
      </div>
    );
  }
  const images = result.data;
  return (
    <div className="relative min-h-screen w-full pb-10 md:pb-[100px]">
      <GridBackground className=" pt-14 pb-4 bg-background h-full flex flex-col">
        <PageHeader
          title="Explore our gallery"
          text="Envision your stay through our gallery of breathtaking room views"
        />
      </GridBackground>
      <div className="px-4  max-w-6xl mx-auto">
        <Suspense fallback={<GallerySkeleton />}>
          <GalleryComponent images={images} />
        </Suspense>
      </div>
    </div>
  );
};

export default GalleryPage;
