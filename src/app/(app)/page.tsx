import { FeaturesSection } from "@/components/FeaturesSection";
import HeroSection from "@/components/HeroSection";
import SectionHeader from "@/components/SectionHeader";
import VideoSection from "@/components/VideoSection";
import StorySection from "@/components/StorySection";
import AccommodationLayout from "@/components/accommodation-layout";
import { Feedback } from "@/components/Feedback";
import { FaqSection } from "@/components/blocks/faq";
import ReviewScroller from "@/components/review-scroller";
import DateRangePicker from "@/components/DateRangePicker";

const items = [
  {
    question: "Are you located near the beach?",
    answer: "Yes we are located near Diani beach",
  },
];

export default function Home() {
  return (
    <div className="relative pb-20 gap-16">
      <div className="h-screen flex flex-col">
        <div className="h-full pt-2 px-2 rounded-2xl">
          <HeroSection />
        </div>
        <div className="pb-2">
          <DateRangePicker />
        </div>
      </div>
      <div className="min-h-[100vh] my-20 text-center">
        <SectionHeader
          title="A serene sanctuary by the Kenyan shore"
          text="Find your coastal escape at Silent Palms Villa in Diani, Kenya. Experience the Indian Ocean while enjoying the comforts of our meticulously designed accommodations. Ideal for unforgettable vacations."
        />
        <div className="flex-grow text-left mt-10 px-4 text-black h-full">
          <StorySection />
        </div>
      </div>
      <div className="h-full my-20 text-center flex flex-col">
        <SectionHeader
          title="Experience Our Difference"
          text="Our unique blend of personalized service, prime location, and luxury amenities sets us apart."
        />
        <div className="flex-grow text-left mt-10 w-7xl mx-auto text-black">
          <FeaturesSection />
        </div>
      </div>
      <div className="my-20 text-center flex flex-col">
        <SectionHeader
          title="Featured Accommodation"
          text="Space and style combine in our featured 2 Bedroom Executive Accommodation."
        />

        <div className=" w-full mt-10 text-left h-full">
          <AccommodationLayout />
        </div>
      </div>
      <div className="flex flex-col my-20 text-center">
        <SectionHeader
          title="As Daylight Fades, Beauty Awakens. Experience Diani Beach's Mesmerizing
Sunset."
          className="max-w-4xl"
        />
        <div className="flex-grow mt-10">
          <VideoSection />
        </div>
      </div>

      <div className="my-20 text-center ">
        <SectionHeader
          title="Testimonials"
          text="Hear what our guests have to say about their unforgettable stays with us."
        />
        <div className="mt-10 text-left max-w-7xl mx-auto">
          <ReviewScroller />
        </div>
      </div>
      <div className="h-[50vh] my-20 text-center flex flex-col">
        <SectionHeader
          title="Frequently Asked Questions"
          text="Find quick answers about your stay, booking details, and villa amenities here."
        />
        <div className="flex-grow items-center justify-center w-3xl mx-auto mt-10 text-left text-lg">
          <FaqSection items={items} />
        </div>
      </div>
      <div className="flex items-center justify-center">
        <Feedback />
      </div>
    </div>
  );
}
