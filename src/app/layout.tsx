import type { <PERSON>ada<PERSON> } from "next";
import "./globals.css";
import { ThemeProvider } from "@/components/theme-provider";
import { poppins } from "./fonts";
import { ReservationProvider } from "@/context/ReservationContext";
import { Toaster } from "@/components/ui/sonner";

export const metadata: Metadata = {
  title: "Silent Palms Villa",
  description:
    "Escape to a peaceful and welcoming haven in our family friendly Villa with a swimming pool. Nested in a tranquil,off Diani beach road, Kwale county",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={`${poppins.className} antialiased`}>
        <ReservationProvider>
          <ThemeProvider
            attribute="class"
            defaultTheme="system"
            enableSystem
            disableTransitionOnChange
          >
            {children}
            <Toaster richColors />
          </ThemeProvider>
        </ReservationProvider>
      </body>
    </html>
  );
}
