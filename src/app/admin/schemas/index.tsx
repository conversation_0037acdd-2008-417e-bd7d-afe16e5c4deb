import z, { string } from "zod";

export const formSchema = z.object({
  name: z
    .string({ error: "Required field" })
    .min(1, { error: "More than two characters required" }),
  category: z
    .string({ error: "Required field" })
    .min(6, { error: "Select a category" }),
  capacity: z.coerce
    .number<number>({
      error: (issue) =>
        issue.input === undefined
          ? "This field is required"
          : "Number required",
    })
    .min(2, { error: "Minimum 2 guests required" }),
  amount: z.coerce
    .number<number>({
      error: (issue) =>
        issue.input === undefined
          ? "This field is required"
          : "Number required",
    })
    .min(1000, { error: "Minimum amount is 1000" }),
  bedrooms: z.coerce
    .number<number>({
      error: (issue) =>
        issue.input === undefined
          ? "This field is required"
          : "Number required",
    })
    .min(1, { error: "Minimum number is 1" }),
  description: z
    .string({ error: "Required field" })
    .min(100, { error: "Minimum 100 characters required" }),
  summary: z
    .string({ error: "Required field" })
    .min(20, { error: "Minimum 20 characters required" }),
  images: z
    .array(
      z.object({
        file: z.any(),
        category: z.string(),
      }),
    )
    .optional(),
});

export const EditAccommodationSchema = z.object({
  name: z
    .string({ error: "Required field" })
    .min(1, { error: "More than two characters required" }),
  category: z.object({
    name: z.string(),
    id: z.string(),
  }),
  capacity: z.coerce
    .number<number>({
      error: (issue) =>
        issue.input === undefined
          ? "This field is required"
          : "Number required",
    })
    .min(2, { error: "Minimum 2 guests required" }),
  pricePerNight: z.coerce
    .number<number>({
      error: (issue) =>
        issue.input === undefined
          ? "This field is required"
          : "Number required",
    })
    .min(1000, { error: "Minimum amount is 1000" }),
  bedrooms: z.coerce
    .number<number>({
      error: (issue) =>
        issue.input === undefined
          ? "This field is required"
          : "Number required",
    })
    .min(1, { error: "Minimum number is 1" }),
  description: z
    .string({ error: "Required field" })
    .min(100, { error: "Minimum 100 characters required" }),
  summary: z
    .string({ error: "Required field" })
    .min(20, { error: "Minimum 20 characters required" }),
  images: z
    .array(
      z.object({
        file: z.any(),
        category: z.string(),
        url: string().optional(),
      }),
    )
    .optional(),
});
