import { getBookings } from "../actions/bookings";
import AdminPageHeader from "../components/admin-page-header";
import BookingsTable from "../components/BookingsTable";

const BookingsPage = async () => {
  const bookings = await getBookings();

  return (
    <div>
      <AdminPageHeader title="Bookings" text="A list of all bookings made" />
      <BookingsTable initialData={bookings} />
    </div>
  );
};

export default BookingsPage;
