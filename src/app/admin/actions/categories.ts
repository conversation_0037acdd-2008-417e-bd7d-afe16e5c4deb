import { cache } from "react";

const API_URL = process.env.NEXT_PUBLIC_API_URL;

export const getCategories = cache(async () => {
  try {
    const response = await fetch(`${API_URL}/api/categories`, {
      cache: "force-cache",
      next: { tags: ["categories"] },
    });
    if (!response.ok) {
      return {
        success: false,
        message: "An error occured on the server",
      };
    }
    const categories = await response.json();
    if (!categories || categories.length <= 0) {
      return {
        success: false,
        message: "No categories found",
      };
    }
    return {
      success: true,
      data: categories,
    };
  } catch (error) {
    console.error(`Internal server error: ${error}`);
    return {
      success: false,
      message: `Internal server error`,
    };
  }
});
