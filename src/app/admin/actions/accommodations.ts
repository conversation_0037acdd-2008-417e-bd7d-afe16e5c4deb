"use server";

import { cache } from "react";
import { getCategories } from "./categories";
import type { AccommodationData, Category } from "../types";
import { revalidatePath, revalidateTag } from "next/cache";

const API_URL = process.env.NEXT_PUBLIC_API_URL;

export const getAccommodations = cache(async () => {
  try {
    const accommodationData = await fetch(`${API_URL}/api/accommodations`, {
      cache: "force-cache",
      next: { tags: ["accommodations"] },
    });
    const accommodations = await accommodationData.json();
    if (!accommodations || accommodations.length <= 0) {
      return {
        success: false,
        message: "No accommodations found",
      };
    }
    const categoriesData = await getCategories();

    const categories: Category[] = Array.isArray(categoriesData.data)
      ? categoriesData.data
      : [];
    const allAccommodations = accommodations.map((acc: AccommodationData) => {
      const cat = categories.find((c) => c.id === acc.categoryId);
      return {
        id: acc.id,
        name: acc.name,
        summary: acc.summary,
        pricePerNight: acc.pricePerNight,
        category: {
          name: cat ? cat.name : "",
          id: cat ? cat.id : "",
        },
        images: acc.images,
        bedrooms: acc.bedrooms,
        capacity: acc.capacity,
        description: acc.description,
      };
    });
    return allAccommodations;
  } catch (error) {
    console.error(`Error fetching accommodations: ${error}`);
    return {
      success: false,
      message: `Could not fetch Accommodations`,
    };
  }
});

export const updateAccommodation = async (
  updatedAccommodation: AccommodationData,
) => {
  if (!updatedAccommodation || !updatedAccommodation.id) {
    return {
      success: false,
      message: "No accommodation data provided",
    };
  }
  const id = updatedAccommodation.id;
  try {
    const payload = {
      name: updatedAccommodation.name,
      description: updatedAccommodation.description,
      summary: updatedAccommodation.summary,
      bedrooms: updatedAccommodation.bedrooms,
      capacity: updatedAccommodation.capacity,
      pricePerNight: updatedAccommodation.pricePerNight,
      categoryId: updatedAccommodation.category.id,
    };
    const response = await fetch(`${API_URL}/api/accommodations/${id}/update`, {
      method: "PATCH",
      body: JSON.stringify(payload),
    });
    if (!response.ok) {
      return {
        success: false,
        message: "An error occured on the server",
      };
    }
    (revalidateTag("accommodations"), revalidatePath("/admin/accommodations"));
    return {
      success: true,
      message: "Accommodation updated successfully",
    };
  } catch (error) {
    console.error(`Error updating accommodation: ${error}`);
    return {
      success: false,
      message: `Could not update Accommodation`,
    };
  }
};
