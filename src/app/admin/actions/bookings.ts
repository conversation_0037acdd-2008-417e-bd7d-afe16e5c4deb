/* eslint-disable @typescript-eslint/no-explicit-any */
"use server";

import type { Booking } from "@/types";
import { getAccommodations } from "./accommodations";

const API_URL = process.env.NEXT_PUBLIC_API_URL;

// TODO: Remove full payment field from return
export const getBookings = async () => {
  try {
    const data = await fetch(`${API_URL}/api/bookings`);
    const bookings = await data.json();

    if (!bookings || bookings.length <= 0) {
      return [];
    }

    // Fetch accommodations data
    const accommodationsData = await getAccommodations();
    const accommodations = Array.isArray(accommodationsData)
      ? accommodationsData
      : [];

    const allBookings = bookings.map((booking: any) => {
      const acc = accommodations.find((a) => a.id === booking.accommodationId);
      return {
        bookingId: booking.bookingId,
        accommodation: acc ? acc.name : booking.accommodationId,
        status: booking.status,
        guestName: `${booking.guestFirstName} ${booking.guestLastName}`,
        guestPhone: booking.guestPhoneNumber,
        guestEmail: booking.guestEmail,
        startDate: booking.startDate,
        endDate: booking.endDate,
        depositAmount: booking.depositAmount,
        totalAmount: booking.totalAmount,
        fullPayment: booking.isFullAmountPaid,
      };
    });

    return allBookings as Booking[];
  } catch (error) {
    console.error(`An error occured while fetching bookings: ${error}`);
    return [];
  }
};
