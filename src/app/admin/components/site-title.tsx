"use client";

import { usePathname } from "next/navigation";

const SiteTitle = () => {
  const pathName = usePathname();
  let title: string | undefined = "";
  const pathnameLength: number = pathName.split("/").length;

  if (pathnameLength === 2) {
    title = "Dashboard";
  } else {
    title = pathName.split("/").pop();
  }

  return <h1 className="text-base font-medium capitalize">{title}</h1>;
};

export default SiteTitle;
