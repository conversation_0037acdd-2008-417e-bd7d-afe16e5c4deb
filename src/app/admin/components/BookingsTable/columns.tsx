"use client";

import { ColumnDef } from "@tanstack/react-table";
import { Button } from "@/components/ui/button";
import { MoreHorizontal } from "lucide-react";

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

import { type Booking } from "@/types";

interface ColumnActions {
  onConfirmFullPayment?: (bookingId: string) => void;
  onDelete?: (bookingId: string) => void;
}

export const createColumns = (): ColumnDef<Booking>[] => {
  const columns: ColumnDef<Booking>[] = [
    {
      accessorKey: "bookingId",
      header: "Booking ID",
    },
    {
      accessorKey: "accommodation",
      header: "Accommodation",
    },
    {
      accessorKey: "status",
      header: "Status",
    },
    {
      accessorKey: "guestName",
      header: "Name",
    },
    {
      accessorKey: "guestPhone",
      header: "Phone",
    },
    {
      accessorKey: "startDate",
      header: "Check In",
    },
    {
      accessorKey: "endDate",
      header: "Check Out",
    },
    {
      accessorKey: "guestEmail",
      header: "Guest Email",
    },
    {
      accessorKey: "depositAmount",
      header: "Expected Deposit",
    },
    {
      accessorKey: "totalAmount",
      header: "Expected Amount",
    },
    {
      accessorKey: "fullPayment",
      header: "Fully paid",
    },
  ];

  columns.push({
    id: "actions",
    header: "Actions",
    cell: ({ row, table }) => {
      const record = row.original;
      const { onConfirmFullPayment, onDelete } = table.options
        .meta as ColumnActions;

      return (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="h-8 w-8 p-0">
              <span className="sr-only">Open menu</span>
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuLabel>Actions</DropdownMenuLabel>

            {onConfirmFullPayment && (
              <DropdownMenuItem
                onClick={() => onConfirmFullPayment(record.bookingId)}
              >
                Confirm Full Payment
              </DropdownMenuItem>
            )}
            {onDelete && (
              <DropdownMenuItem onClick={() => onDelete(record.bookingId)}>
                Delete
              </DropdownMenuItem>
            )}
          </DropdownMenuContent>
        </DropdownMenu>
      );
    },
  });
  return columns;
};
