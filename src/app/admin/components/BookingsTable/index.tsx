"use client";

import { BookingsDataTable } from "./BookingsDataTable";
import { createColumns } from "./columns";
//import { toast } from "sonner";
import { type Booking } from "@/types";
import { useState } from "react";

interface BookingsTableProps {
  initialData: Booking[];
}

const BookingsTable = ({ initialData }: BookingsTableProps) => {
  const [data, setData] = useState<Booking[]>(initialData);

  const handleDelete = (bookingId: string) => {
    setData(data.filter((record) => record.bookingId !== bookingId));
  };

  const columns = createColumns();
  return (
    <div>
      <BookingsDataTable
        columns={columns}
        data={data}
        onDelete={handleDelete}
      />
    </div>
  );
};

export default BookingsTable;
