import { CirclePlus, HouseIcon } from "lucide-react";

import { Badge } from "@/components/ui/badge";
import { ScrollArea, ScrollBar } from "@/components/ui/scroll-area";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import AdminPageHeader from "../admin-page-header";
import ListingsTab from "./ListingsTab";
import CreateTab from "./CreateTab";
import { getAccommodations, getCategories } from "../../actions";
import type { Category } from "../../types";

const AccommodationContent = async () => {
  const accommodations = await getAccommodations();

  const result = await getCategories();

  let categories: Category[] = [];

  if (result && result.data) {
    categories = result.data;
  }

  return (
    <Tabs defaultValue="tab-1">
      <ScrollArea>
        <TabsList className="text-foreground mb-3 h-auto gap-2 rounded-none border-b bg-transparent px-0 py-1">
          <TabsTrigger
            value="tab-1"
            className="hover:bg-accent hover:text-foreground data-[state=active]:after:bg-primary data-[state=active]:hover:bg-accent relative after:absolute after:inset-x-0 after:bottom-0 after:-mb-1 after:h-0.5 data-[state=active]:bg-transparent data-[state=active]:shadow-none"
          >
            <HouseIcon
              className="-ms-0.5 me-1.5 opacity-60"
              size={16}
              aria-hidden="true"
            />
            Accommodations
            <Badge
              className="bg-primary/15 ms-1.5 min-w-5 px-1"
              variant="secondary"
            >
              {accommodations.length}
            </Badge>
          </TabsTrigger>
          <TabsTrigger
            value="tab-2"
            className="hover:bg-accent hover:text-foreground data-[state=active]:after:bg-primary data-[state=active]:hover:bg-accent relative after:absolute after:inset-x-0 after:bottom-0 after:-mb-1 after:h-0.5 data-[state=active]:bg-transparent data-[state=active]:shadow-none"
          >
            <CirclePlus
              className="-ms-0.5 me-1.5 opacity-60"
              size={16}
              aria-hidden="true"
            />
            Add Accommodation
          </TabsTrigger>
        </TabsList>
        <ScrollBar orientation="horizontal" />
      </ScrollArea>
      <TabsContent value="tab-1">
        <AdminPageHeader
          title="Accommodation Listings"
          text="View, edit or manage all accommodations, Keep the information up-to-date to provide the best experience for users"
        />
        <ListingsTab accommodations={accommodations} categories={categories} />
      </TabsContent>
      <TabsContent value="tab-2">
        <AdminPageHeader
          title="Create New Accommodation"
          text="Create a new accommodation to be added to available accommodations, be sure to include everything"
        />
        <CreateTab categories={categories} />
      </TabsContent>
    </Tabs>
  );
};

export default AccommodationContent;
