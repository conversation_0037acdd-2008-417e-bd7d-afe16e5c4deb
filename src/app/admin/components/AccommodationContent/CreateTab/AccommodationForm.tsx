/* eslint-disable @typescript-eslint/no-explicit-any */

"use client";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Form,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Loader2 } from "lucide-react";

interface AccommodationFormProps {
  form: any;
  onSubmit: (values: any) => void;
  categories: {
    name: string;
    id: string;
  }[];
  isSubmitting: boolean;
}

export default function AccommodationForm({
  form,
  onSubmit,
  categories,
  isSubmitting,
}: AccommodationFormProps) {
  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(onSubmit)}
        className="space-y-8 max-w-lg py-6"
      >
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Name</FormLabel>
              <Input
                placeholder="e.g C2"
                type="text"
                value={field.value}
                onChange={field.onChange}
                autoFocus
              />
              <FormDescription>
                This is the display name for the accommodation
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="grid grid-cols-2 items-start gap-2">
          <FormField
            control={form.control}
            name="category"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Category</FormLabel>
                <Select
                  onValueChange={field.onChange}
                  defaultValue={field.value}
                >
                  <SelectTrigger className="w-full">
                    <SelectValue
                      placeholder="Select a category"
                      className="w-full"
                    />
                  </SelectTrigger>
                  <SelectContent>
                    {categories?.map(({ name, id }) => (
                      <SelectItem value={id} key={id}>
                        {name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormDescription>The category it belongs to</FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="capacity"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Capacity</FormLabel>
                <Input
                  placeholder="e.g 2"
                  type="number"
                  value={field.value}
                  onChange={field.onChange}
                />
                <FormDescription>Number of guests</FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <div className="grid grid-cols-2 items-start gap-2">
          <FormField
            control={form.control}
            name="amount"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Amount</FormLabel>
                <Input
                  placeholder="e.g 15000"
                  value={field.value}
                  onChange={field.onChange}
                />
                <FormDescription>Amount per night </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="bedrooms"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Bedrooms</FormLabel>
                <Input placeholder="e.g 2" type="number" {...field} />
                <FormDescription>Number of bedrooms present</FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
        <FormField
          control={form.control}
          name="summary"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Summary</FormLabel>
              <Input
                placeholder="Suitable for families......"
                value={field.value}
                onChange={field.onChange}
              />

              <FormDescription>
                A brief summary of the accommodation 13 words or less
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="description"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Description</FormLabel>
              <Textarea
                placeholder="The accommodation features....."
                className="resize-y min-h-24"
                value={field.value}
                onChange={field.onChange}
              />
              <div className="text-sm text-muted-foreground">
                {field.value?.length || 0}/100 characters minimum
              </div>
              <FormMessage />
            </FormItem>
          )}
        />
        <Button
          size={"lg"}
          type="submit"
          className="rounded cursor-pointer disabled:cursor-wait"
          disabled={isSubmitting}
        >
          {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
          {isSubmitting ? "Submitting..." : "Submit"}
        </Button>
      </form>
    </Form>
  );
}
