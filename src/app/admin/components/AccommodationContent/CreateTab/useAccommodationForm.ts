import { useForm } from "react-hook-form";
import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { formSchema } from "@/app/admin/schemas";

interface UseAccommodationFormProps {
  onSuccess: (message: string) => void;
  onError: (message: string) => void;
}

export const useAccommodationForm = ({
  onSuccess,
  onError,
}: UseAccommodationFormProps) => {
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: "",
      category: "",
      amount: 0,
      bedrooms: 0,
      capacity: 0,
      description: "",
      summary: "",
      images: [],
    },
  });

  const onSubmit = async (
    values: z.infer<typeof formSchema>
  ): Promise<void> => {
    try {
      const formData = new FormData();

      // Append all form fields
      for (const key in values) {
        if (key !== "images") {
          const value = values[key as keyof typeof values];
          formData.append(key, String(value));
        }
      }

      // Append image files with their categories
      values.images?.forEach((image, index) => {
        formData.append(`images[${index}].file`, image.file);
        formData.append(`images[${index}].category`, image.category);
      });

      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_URL}/api/accommodations`,
        {
          method: "POST",
          body: formData,
        }
      );

      const result = await response.json();

      if (!response.ok) {
        console.log(`HTTP error! status: ${response.status}`);
        onError(
          result.message || "An error occurred while creating accommodation"
        );
        return;
      }

      onSuccess(result.message);
    } catch (error) {
      console.error("Form submission error:", error);
      onError("Form submission error");
    }
  };

  return { form, onSubmit };
};
