"use client";

import { useState } from "react";
import { ImageUpload } from "./AccommodationUpload";
import AccommodationForm from "./AccommodationForm";
import { useAccommodationForm } from "./useAccommodationForm";
import { toast } from "sonner";
import { z } from "zod";
import { formSchema } from "@/app/admin/schemas";

export interface IUploadedImage {
  file: File;
  category: string;
}

type CreateTabProps = {
  name: string;
  id: string;
};

const CreateTab = ({ categories }: { categories: CreateTabProps[] }) => {
  const [uploadedImages, setUploadedImages] = useState<IUploadedImage[]>([]);

  const handleImageUpload = (file: File, category: string) => {
    setUploadedImages((prevImages) => [...prevImages, { file, category }]);
  };

  const { form, onSubmit } = useAccommodationForm({
    onSuccess: (message) => {
      toast.success(message);
      form.reset();
    },
    onError: (message) => {
      toast.error(message);
    },
  });

  const handleSubmit = async (values: z.infer<typeof formSchema>) => {
    await onSubmit({ ...values, images: uploadedImages });
  };

  return (
    <div>
      <div className="mb-2 flex items-center gap-2">
        <ImageUpload category="Cover" onImageUpload={handleImageUpload} />
        <ImageUpload category="Living Room" onImageUpload={handleImageUpload} />
        <ImageUpload category="Kitchen" onImageUpload={handleImageUpload} />
        <ImageUpload category="Bedroom" onImageUpload={handleImageUpload} />
        <ImageUpload category="Exterior" onImageUpload={handleImageUpload} />
      </div>
      <AccommodationForm
        categories={categories}
        form={form}
        onSubmit={handleSubmit}
        isSubmitting={form.formState.isSubmitting}
      />
    </div>
  );
};

export default CreateTab;
