import type { AccommodationData, Category } from "../../types";
import AccommodationCard from "../accommodation-card";

interface ListingsTabProps {
  accommodations: AccommodationData[];
  categories: Category[];
}

const ListingsTab = ({ accommodations, categories }: ListingsTabProps) => {
  return (
    <div>
      {accommodations.length > 0 ? (
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-2">
          {accommodations.map((accommodation) => (
            <AccommodationCard
              key={accommodation.id}
              accommodation={accommodation}
              categories={categories}
            />
          ))}
        </div>
      ) : (
        <div>
          <p className="text-center">No accommodations found</p>
        </div>
      )}
    </div>
  );
};

export default ListingsTab;
