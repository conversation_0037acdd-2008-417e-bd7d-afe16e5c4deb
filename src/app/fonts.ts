import localFont from "next/font/local";

export const poppins = localFont({
  src: [
    {
      path: "../fonts/poppins/Poppins-Light.ttf",
      weight: "300",
    },
    {
      path: "../fonts/poppins/Poppins-Regular.ttf",
      weight: "400",
    },
    {
      path: "../fonts/poppins/Poppins-Medium.ttf",
      weight: "500",
    },
    {
      path: "../fonts/poppins/Poppins-SemiBold.ttf",
      weight: "600",
    },
    {
      path: "../fonts/poppins/Poppins-Bold.ttf",
      weight: "700",
    },
    {
      path: "../fonts/poppins/Poppins-Black.ttf",
      weight: "900",
    },
  ],
  display: "swap",
});

export const anton = localFont({
  src: "../fonts/Anton/Anton-Regular.woff2",
  weight: "400",
  display: "swap",
});

export const cofa_sans = localFont({
  src: "../fonts/cofo-sans/cofo-sans.otf",
  weight: "400",
  display: "swap",
});

export const sang_blue = localFont({
  src: [
    {
      path: "../fonts/sang-blue/SangBleuKingdom-Regular.woff",
      weight: "400",
    },
    {
      path: "../fonts/sang-blue/SangBleuKingdom-Medium.woff",
      weight: "500",
    },
  ],
  display: "swap",
});
