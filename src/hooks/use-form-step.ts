import { FieldValues } from "react-hook-form";
import { useFormStore } from "@/lib/store";

interface UseFormStepProps {
  currentStep: number;
}

export function useFormStep<T extends FieldValues>({
  currentStep,
}: UseFormStepProps) {
  const { setCurrentStep, setFormData, getLatestState } = useFormStore();

  const handleNext = (data: T) => {
    setFormData(data);
    setCurrentStep(currentStep + 1);
  };

  const handleBack = () => {
    setCurrentStep(currentStep - 1);
  };

  return {
    setFormData,
    handleNext,
    handleBack,
    setCurrentStep,
    getLatestState,
  };
}
