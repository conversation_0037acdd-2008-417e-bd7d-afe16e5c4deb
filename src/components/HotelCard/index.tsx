"use client";
import { cn } from "@/lib/utils";

interface HotelCardProps {
  name: string;
  location: string;
  isAvailable: boolean;
}

const HotelCard = ({ name, location, isAvailable }: HotelCardProps) => {
  return (
    <div
      className={cn("border rounded-lg p-4", { "bg-red-200": !isAvailable })}
    >
      <div className="w-full h-48 bg-green-500 animate-pulse rounded-lg mb-4"></div>
      <h2 className="text-xl font-bold">{name}</h2>
      <p className="text-gray-500">{location}</p>
      <p
        className={cn("text-lg font-bold", {
          "text-green-500": isAvailable,
          "text-red-500": !isAvailable,
        })}
      >
        {isAvailable ? "Available" : "Not Available"}
      </p>
    </div>
  );
};

export default HotelCard;
