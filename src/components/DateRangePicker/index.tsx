"use client";

import { useEffect, useRef, useState } from "react";

import { z } from "zod";
import { useForm } from "react-hook-form";
import { type DateRange } from "react-day-picker";
import { addDays, format } from "date-fns";

import { ArrowRight, BedDouble, CalendarIcon, Users } from "lucide-react";
import { Calendar } from "@/components/ui/calendar";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
} from "@/components/ui/form";
import { Button } from "../ui/button";
import { cn } from "@/lib/utils";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../ui/select";

import AvailabilityDialog from "@/components/AvailabilityChecker/AvailabilityDialog";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";

import { type IAccommodation } from "@/types";
import { getAvailableAccommodations } from "@/app/(app)/reservation/step-one/actions";
import { toast } from "sonner";
import LoadingModal from "../Loaders/LoadingModal";

const MIN_NIGHTS = 4;

const Schema = z
  .object({
    startDate: z.date({ error: "Check-in required" }),
    endDate: z.date({ error: "Check-out required" }),
    guests: z.string({ error: "Guests required" }),
    bedrooms: z.string({ error: "Bedrooms required" }),
  })
  .refine((data) => data.endDate > data.startDate, {
    message: "Check-out must be after check-in",
    path: ["endDate"],
  })
  .refine(
    (data) => {
      const nights = Math.ceil(
        (data.endDate.getTime() - data.startDate.getTime()) /
          (1000 * 60 * 60 * 24)
      );
      return nights >= MIN_NIGHTS;
    },
    {
      message: `Minimum stay is ${MIN_NIGHTS} nights`,
      path: ["endDate"],
    }
  );

const DateRangePicker = () => {
  const [accommodations, setAccommodations] = useState<IAccommodation[] | null>(
    null
  );

  const handleSelect = (id: string) => {
    console.log(id);
  };

  const form = useForm<z.infer<typeof Schema>>({
    resolver: zodResolver(Schema),
    defaultValues: {
      startDate: new Date(),
      endDate: addDays(new Date(), MIN_NIGHTS),
      guests: "2",
      bedrooms: "2",
    },
  });
  const [range, setRange] = useState<DateRange>(() => {
    const today = new Date();
    return { from: today, to: addDays(today, MIN_NIGHTS) };
  });
  const [calendarOpen, setCalendarOpen] = useState(false);

  const [isLoading, setIsLoading] = useState(false);
  const startBtnRef = useRef<HTMLButtonElement | null>(null);
  const guestBtnRef = useRef<HTMLButtonElement | null>(null);
  const [popoverWidth, setPopoverWidth] = useState<number>(0);
  const buttonWidth = startBtnRef.current?.getBoundingClientRect().width;
  const [isOpen, setIsOpen] = useState(false);

  const computePopoverWidth = () => {
    const startRect = startBtnRef.current?.getBoundingClientRect();
    const endRect = guestBtnRef.current?.getBoundingClientRect();
    if (startRect && endRect) {
      setPopoverWidth(endRect.right - startRect.left);
    } else if (startRect) {
      setPopoverWidth(startRect.width);
    }
  };

  useEffect(() => {
    computePopoverWidth();
    const onResize = () => computePopoverWidth();
    window.addEventListener("resize", onResize);
    return () => window.removeEventListener("resize", onResize);
  }, []);

  const [errorsOpen, setErrorsOpen] = useState(false);
  useEffect(() => {
    setErrorsOpen(Object.keys(form.formState.errors).length > 0);
  }, [form.formState.errors]);

  async function onSubmit(values: z.infer<typeof Schema>) {
    setIsLoading(true);
    try {
      const startDate = format(values.startDate, "yyyy-MM-dd");
      const endDate = format(values.endDate, "yyyy-MM-dd");
      const guests = Number(values.guests);
      const bedrooms = Number(values.bedrooms);

      const response = await getAvailableAccommodations(
        startDate,
        endDate,
        guests,
        bedrooms
      );

      if (!response.success) {
        toast.error("Error", {
          description: response.message,
        });
        return;
      }
      const availableAccommodations = response.data;
      setAccommodations(availableAccommodations);
      setIsOpen(true);
    } catch (error) {
      console.log(`An error occurred: ${error}`);
      return;
    } finally {
      setIsLoading(false);
    }
  }

  return (
    <>
      <LoadingModal
        isOpen={isLoading}
        message="Fetching Accommodations"
        text="Finding available accommodations..."
      />

      <section className="w-full px-2 pb-2 pt-2">
        <AvailabilityDialog
          isOpen={isOpen}
          onOpenChange={setIsOpen}
          accommodations={accommodations}
          onSelect={handleSelect}
        />
        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(onSubmit)}
            className=" rounded-lg  bg-primary py-4  text-white shadow-xl ring-1 ring-white/10 backdrop-blur"
          >
            <div className="flex items-end px-32 justify-center gap-2 py-2">
              {/* Check-in */}
              <FormField
                control={form.control}
                name="startDate"
                render={({ field }) => (
                  <FormItem className="flex flex-col flex-1 relative">
                    <FormLabel className="text-emerald-100 data-[error=true]:text-emerald-100">
                      Arrival
                    </FormLabel>
                    <Popover open={calendarOpen} onOpenChange={setCalendarOpen}>
                      <PopoverTrigger asChild>
                        <FormControl>
                          <Button
                            ref={startBtnRef}
                            type="button"
                            variant="outline"
                            className={cn(
                              "h-12 w-full justify-start bg-white text-black aria-invalid:ring-0 aria-invalid:border-input cursor-pointer"
                            )}
                            onClick={() => {
                              computePopoverWidth();
                              setCalendarOpen(true);
                            }}
                          >
                            <span>
                              <CalendarIcon className="mr-2 h-4 w-4" />
                            </span>
                            {field.value
                              ? format(field.value, "PPP")
                              : format(new Date(), "PPP")}
                          </Button>
                        </FormControl>
                      </PopoverTrigger>
                      <PopoverContent
                        className="p-0"
                        align="start"
                        style={{ width: popoverWidth || undefined }}
                      >
                        <Calendar
                          mode="range"
                          numberOfMonths={2}
                          selected={range}
                          defaultMonth={range.from ?? new Date()}
                          onSelect={(r) => {
                            setRange(r ?? { from: undefined, to: undefined });
                            if (r?.from) form.setValue("startDate", r.from);
                            if (r?.to) form.setValue("endDate", r.to);
                          }}
                          disabled={{ before: new Date() }}
                          className="rounded-none w-full"
                        />
                      </PopoverContent>
                    </Popover>
                  </FormItem>
                )}
              />

              {/* Check-out */}
              <FormField
                control={form.control}
                name="endDate"
                render={({ field }) => (
                  <FormItem className="flex flex-col flex-1">
                    <FormLabel className="text-emerald-100 data-[error=true]:text-emerald-100">
                      Departure
                    </FormLabel>
                    <FormControl>
                      <Button
                        type="button"
                        variant="outline"
                        className="h-12 w-full justify-start bg-white text-black aria-invalid:ring-0 aria-invalid:border-input cursor-pointer"
                        onClick={() => {
                          computePopoverWidth();
                          setCalendarOpen(true);
                        }}
                      >
                        <span>
                          <CalendarIcon className="mr-2 h-4 w-4" />
                        </span>
                        {field.value
                          ? format(field.value, "PPP")
                          : format(addDays(new Date(), MIN_NIGHTS), "PPP")}
                      </Button>
                    </FormControl>
                  </FormItem>
                )}
              />

              {/* Guests */}
              <FormField
                control={form.control}
                name="guests"
                render={({ field }) => (
                  <FormItem className="flex flex-col flex-1">
                    <FormLabel className="text-emerald-100 data-[error=true]:text-emerald-100">
                      Guests
                    </FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger
                          ref={guestBtnRef}
                          className=" cursor-pointer h-12 w-full bg-white text-black aria-invalid:ring-0 aria-invalid:border-input font-medium relative"
                          size="sm"
                        >
                          <span>
                            <Users className="mr-2 h-4 w-4" />
                          </span>
                          <SelectValue placeholder="3" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {Array.from({ length: 6 }, (_, i) => String(i + 1)).map(
                          (n) => (
                            <SelectItem key={n} value={n}>
                              {n}
                            </SelectItem>
                          )
                        )}
                      </SelectContent>
                    </Select>
                  </FormItem>
                )}
              />

              {/* Bedrooms */}
              <FormField
                control={form.control}
                name="bedrooms"
                render={({ field }) => (
                  <FormItem className="flex flex-col flex-1">
                    <FormLabel className="text-emerald-100 data-[error=true]:text-emerald-100">
                      Bedrooms
                    </FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger
                          size="sm"
                          className="cursor-pointer h-12 w-full bg-white  text-black aria-invalid:ring-0 aria-invalid:border-input font-medium"
                        >
                          <span>
                            <BedDouble className="mr-2 h-4 w-4" />
                          </span>

                          <SelectValue placeholder="1" className="text-black" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {Array.from({ length: 2 }, (_, i) => String(i + 1)).map(
                          (n) => (
                            <SelectItem key={n} value={n}>
                              {n}
                            </SelectItem>
                          )
                        )}

                        <Dialog open={errorsOpen} onOpenChange={setErrorsOpen}>
                          <DialogContent>
                            <DialogHeader>
                              <DialogTitle>Form errors</DialogTitle>
                              <DialogDescription>
                                Please fix the following and try again:
                              </DialogDescription>
                            </DialogHeader>
                            <ul className="list-disc pl-5 space-y-1 text-sm">
                              {Object.entries(form.formState.errors).map(
                                ([key, err]) => (
                                  <li key={key} className="text-red-600">
                                    {err?.message as string}
                                  </li>
                                )
                              )}
                            </ul>
                          </DialogContent>
                        </Dialog>
                      </SelectContent>
                    </Select>
                  </FormItem>
                )}
              />

              {/* Submit */}

              <Button
                type="submit"
                variant={"outline"}
                className="h-12 flex-1 justify-center uppercase bg-black border-black text-white hover:text-black cursor-pointer hover:bg-amber-300 hover:border-amber-300"
                style={{
                  maxWidth: buttonWidth || 200,
                }}
              >
                Book Now
                <ArrowRight className="ml-2 w-4 h-4" />
              </Button>
            </div>
          </form>
        </Form>
      </section>
    </>
  );
};

export default DateRangePicker;
