import storyImage from "../../public/story.webp";
import Image from "next/image";

const descriptions = [
  "The premise offers luxurious two-bedroom units, featuring serene gardens near Diani beach, providing easy shoreline access and a prime location for tranquil getaways.",
  "With its rooftop ocean outlook and private terraces, the villa combines luxury and nature. Self-catering units feature en-suite bedrooms and open living areas for 4-6 guests.",
  "Versatile accommodation suits families, couples, and groups seeking relaxation or excitement. Long-term packages offered. Privacy-centric design adapts to different vacation preferences.",
];

const StorySection = () => {
  return (
    <div className="grid grid-cols-1 lg:grid-cols-[412px_1fr] h-full ">
      <div className="flex items-end pb-8">
        <div>
          <p className=" max-w-[350px]  text-neutral-600 dark:text-neutral-300 text-wrap">
            {descriptions[0]}
          </p>
        </div>
      </div>
      <div className="flex flex-col h-full">
        <div className="pb-16">
          <div className="flex justify-between gap-6">
            <div className="max-w-sm">
              <p className="max-w-[350px]  text-neutral-600 dark:text-neutral-300 text-wrap">
                {descriptions[2]}
              </p>
            </div>
            <div className="max-w-sm">
              <p className=" text-neutral-600 dark:text-neutral-300 text-wrap">
                {descriptions[1]}
              </p>
            </div>
          </div>
        </div>
        <div className="relative rounded-lg flex-grow h-[465px] w-full  bg-gray-800">
          <Image
            src={storyImage}
            alt="storyImage"
            fill
            priority
            loading="eager"
            quality={90}
            sizes="(max-width:640px) 100vw, 456px"
            className="object-cover rounded-lg"
          />
        </div>
      </div>
    </div>
  );
};

export default StorySection;
