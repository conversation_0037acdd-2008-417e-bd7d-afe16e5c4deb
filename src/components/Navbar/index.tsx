import Link from "next/link";
import Large from "./Large";
import Image from "next/image";

const Navbar = () => {
  return (
    <div className="fixed top-0  right-2 left-2 z-[100] inset-x-0">
      <nav className="grid grid-cols-1 md:grid-cols-2 py-3 text-white w-full items-center px-4">
        <div className=" flex items-center gap-6 ">
          <Link href="/" className="flex items-center space-x-2">
            <Image
              src="/logo.webp"
              alt="logo"
              width={50}
              height={50}
              className="object-cover"
              priority={true}
              quality={80}
            />
          </Link>
          <Large />
        </div>

        <div className=" h-full flex items-center justify-end">
          <Link
            href="/reservation"
            className="cursor-pointer uppercase bg-slate-200 px-6 w-[120px] rounded-md py-2.5 hover:bg-amber-300/95 hover:text-black transition-colors  shadow-lg border-[0.8px] border-slate-100 hover:border-amber-300 flex text-center justify-center text-primary font-bold text-sm items-center"
          >
            Book Now
          </Link>
        </div>
      </nav>
    </div>
  );
};

export default Navbar;

// TODO: Reference The glass effect later
//
{
  /* <nav className="grid grid-cols-1 backdrop-blur-lg md:grid-cols-2 py-1 w-full items-center border-b-[0.8px] border-neutral-200/50 px-2  bg-white/50 shadow-xs"> */
}
{
  /*         <div className=" flex items-center gap-6 "> */
}
{
  /*           <Link href="/" className="flex items-center space-x-2"> */
}
{
  /*             <Image */
}
{
  /*               src="/logo.webp" */
}
{
  /*               alt="logo" */
}
{
  /*               width={50} */
}
{
  /*               height={50} */
}
{
  /*               className="object-cover" */
}
{
  /*               priority={true} */
}
{
  /*               quality={100} */
}
{
  /*             /> */
}
{
  /*           </Link> */
}
{
  /*           <Large /> */
}
{
  /*         </div> */
}
{
  /**/
}
{
  /*         <div className=" h-full flex items-center justify-end"> */
}
{
  /*           <Link */
}
{
  /*             href="/reservation" */
}
{
  /*             className="cursor-pointer bg-primary px-6 rounded-full py-3 shadow-lg flex text-slate-100 text-sm items-center" */
}
{
  /*           > */
}
{
  /*             Book Now */
}
{
  /*           </Link> */
}
{
  /*         </div> */
}
{
  /*       </nav> */
}
