"use client";

import { NavLinks } from "@/data";
import Link from "next/link";

const Large = () => {
  return (
    <div className="flex justify-center items-center col-span-2">
      <div className="flex items-center space-x-1  text-white relative">
        {NavLinks?.map((item, idx) => (
          <Link
            href={item.link}
            key={idx}
            className=" cursor-pointer text-sm px-2 py-2 font-medium "
          >
            {item.title}
          </Link>
        ))}
      </div>
    </div>
  );
};

export default Large;
