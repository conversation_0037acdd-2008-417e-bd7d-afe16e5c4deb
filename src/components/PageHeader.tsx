import { anton } from "@/app/fonts";

type PageHeaderProps = {
  title: string;
  text?: string;
};

const PageHeader = ({ title, text }: PageHeaderProps) => {
  return (
    <div className="flex flex-col gap-6 max-w-3xl mx-auto text-center pt-20 pb-16">
      <h4
        className={`${anton.className} text-6xl mx-auto text-primary tracking-tight leading-[1.1] ${!text ? "max-w-full" : "max-w-xl"}`}
      >
        {title}
      </h4>
      <p>{text}</p>
    </div>
  );
};

export default PageHeader;
