import type { Review } from "@/types";
import { <PERSON>, CardContent, CardFooter } from "../ui/card";

const ReviewCard = ({ review, name }: Review) => {
  return (
    <Card className="h-full rounded-xl border border-neutral-200 shadow-sm">
      <CardContent className="p-6">
        <div className="text-2xl leading-none text-neutral-400 mb-2">{"“"}</div>
        <p className="text-[15px] leading-6 text-neutral-900 h-[100px] overflow-hidden">
          {review}
        </p>
      </CardContent>
      <CardFooter className="px-6 pb-6">
        <div className="flex items-center gap-3">
          <div className="h-8 w-8 rounded-full bg-neutral-200" />

          <div className="text-sm">
            <div className="font-medium text-neutral-900">{name}</div>
          </div>
        </div>
      </CardFooter>
    </Card>
  );
};

export default ReviewCard;
