"use client";

import type { Review } from "@/types";

import { useEffect, useRef, useState } from "react";
import { Button } from "@/components/ui/button";
import { ChevronLeft, ChevronRight } from "lucide-react";
import ReviewCard from "./ReviewCard";

const reviews: Review[] = [
  {
    review:
      "Big thanks to <PERSON><PERSON><PERSON> for helping me discover new places filled with fun stories and unforgettable moments!",
    name: "<PERSON>",
  },
  {
    review:
      "The tour guide was super enthusiastic! Their love for the history and culture of the places we visited kept everyone engaged and pumped the whole time.",
    name: "<PERSON><PERSON>",
  },
  {
    review:
      "The guide really looked out for our safety and comfort, making sure we were all good throughout the tour. Their attention to detail helped us feel safe and chill.",
    name: "<PERSON>",
  },
  // Extra cards to demonstrate scrolling by 'next set'
  {
    review:
      "Loved every moment! The itinerary was balanced and the pacing perfect—plenty of time to soak in each place.",
    name: "<PERSON><PERSON>",
  },
  {
    review:
      "Our guide’s local tips were gold. We found hidden gems we’d never have seen on our own.",
    name: "<PERSON>",
  },
  {
    review:
      "Super organized and friendly. Felt safe and cared for the whole way through the tour.",
    name: "<PERSON><PERSON>",
  },
];

const GAP_PX = 20; // 20px gap between cards

const ReviewScroller = () => {
  const ref = useRef<HTMLDivElement>(null);
  const [canPrev, setCanPrev] = useState(false);
  const [canNext, setCanNext] = useState(true);

  function updateArrows() {
    const el = ref.current;
    if (!el) return;
    const { scrollLeft, scrollWidth, clientWidth } = el;
    setCanPrev(scrollLeft > 0);
    setCanNext(scrollLeft + clientWidth < scrollWidth - 1);
  }

  function scrollPage(dir: 1 | -1) {
    const el = ref.current;
    if (!el) return;
    const page = el.clientWidth; // exactly 3 cards + 2 gaps
    el.scrollBy({ left: dir * page, behavior: "smooth" });
  }

  useEffect(() => {
    updateArrows();
    const el = ref.current;
    if (!el) return;
    const onScroll = () => updateArrows();
    el.addEventListener("scroll", onScroll, { passive: true });
    const onResize = () => updateArrows();
    window.addEventListener("resize", onResize);
    return () => {
      el.removeEventListener("scroll", onScroll);
      window.removeEventListener("resize", onResize);
    };
  }, []);
  return (
    <div>
      <div className="mb-4 flex justify-end gap-2">
        <Button
          variant="outline"
          size="icon"
          onClick={() => scrollPage(-1)}
          disabled={!canPrev}
          aria-label="Previous reviews"
          className={`${canPrev} ? bg-amber-300  text-black cursor-pointer hover:bg-primary': ""`}
        >
          <ChevronLeft className="h-5 w-5" />
        </Button>
        <Button
          variant={"outline"}
          size="icon"
          onClick={() => scrollPage(1)}
          disabled={!canNext}
          aria-label="Next reviews"
          className={`${canNext} ? bg-amber-300  text-black cursor-pointer hover:bg-primary': ""`}
        >
          <ChevronRight className="h-5 w-5" />
        </Button>
      </div>

      <div
        ref={ref}
        className="relative no-scrollbar flex overflow-x-auto scroll-smooth snap-x snap-mandatory"
        style={{ gap: `${GAP_PX}px` }}
        aria-label="Customer reviews carousel"
      >
        {reviews.map((r, i) => (
          <div
            key={i}
            className="shrink-0 snap-start"
            // Ensure exactly 3 cards fit per view (3 cards + 2 gaps = container width)
            style={{ width: `calc((100% - ${GAP_PX * 2}px) / 3)` }}
          >
            <ReviewCard {...r} />
          </div>
        ))}
      </div>
    </div>
  );
};

export default ReviewScroller;
