import type { IAccommodation } from "@/types";
import Image from "next/image";
import { Button } from "./ui/button";
import { anton } from "@/app/fonts";
import { formatToKsh } from "@/lib/utils";

interface AccommodationCardProps {
  acc: IAccommodation;
  onSelect?: (id: string) => void;
}

const AccommodationCard = ({ acc, onSelect }: AccommodationCardProps) => {
  if (!acc) return <div />;

  const { name, summary, id, pricePerNight, images, bedrooms } = acc;
  return (
    <div className=" rounded-md w-[350px] p-2 border bg-white border-white shadow-xs">
      <div className=" w-full rounded-md h-[170px] relative">
        <Image
          src={images[0].url}
          alt={name}
          fill
          className="rounded-md object-cover"
        />
      </div>
      <div className="space-y-2 px-4 pt-3">
        <div className="flex flex-col">
          <div className="flex items-center justify-between">
            <h6 className={`${anton.className} text-lg font-black`}>
              {name} Accommodation
            </h6>
            <p className="text-sm">
              {bedrooms > 1 ? `${bedrooms} bedrooms` : `${bedrooms} bedroom`}
            </p>
          </div>
          <p className="text-sm text-muted-foreground line-clamp-2">
            {summary}
          </p>
        </div>
        <div className="flex flex-col justify-between gap-4 sm:flex-row sm:items-center">
          <div>
            <p className="text-xs text-slate-400">per night</p>
            <p className={`font-bold uppercase`}>
              {formatToKsh(pricePerNight)}
            </p>
          </div>
          {onSelect && (
            <div>
              <Button type="button" onClick={() => onSelect(id)} className="">
                Select
              </Button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default AccommodationCard;
