import { cn } from "@/lib/utils";
import {
  IconSwimming,
  IconPlant2,
  IconMapPin,
  IconAward,
  IconBed,
  IconHomeSpark,
} from "@tabler/icons-react";

export function FeaturesSection() {
  const features = [
    {
      title: "Poolside Paradise",
      description:
        "Dive into a world of pure delight with our enchanting swimming pool. Surrounded by lush greenery, it offers a serene escape where you can float lazily, soak up the sun, or simply revel in the refreshing water.",
      icon: <IconSwimming />,
    },
    {
      title: "Cozy Comfort",
      description:
        "Experience the pinnacle of comfort on our queen-size beds, ensuring a blissful sleep. Immerse yourself in clean, luxurious linens, and drift off to sweet dreams in a tranquil haven of relaxation.",
      icon: <IconBed />,
    },
    {
      title: "Serene Environment",
      description:
        "Our villa is enveloped by lush greenery, offering a serene sanctuary that rejuvenates the mind, body, and soul. Immerse in tranquil natural beauty and enjoy a peaceful escape.",
      icon: <IconPlant2 />,
    },
    {
      title: "Luxurious Amenities",
      description:
        "Indulge in opulence with our luxury amenities. From the moment you enter our villa, experience a modern kitchen, high-speed internet and many more amenities for seamless comfort and convenience.",
      icon: <IconHomeSpark />,
    },
    {
      title: "Perfect Location",
      description:
        "Embrace shopping at nearby centers, indulge in beach life, discover thrilling activities, explore captivating waterfall caves, and enjoy convenient access to Ukunda Airstrip and wildlife wonders.",
      icon: <IconMapPin />,
    },
    {
      title: "Service Excellence",
      description:
        "Impeccable customer service is our commitment, ensuring every moment of your stay is exceptional. Our attentive team goes above and beyond, providing personalized care and remarkable value for your money.",
      icon: <IconAward />,
    },
  ];
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3  relative z-10 max-w-7xl mx-auto">
      {features.map((feature, index) => (
        <Feature key={feature.title} {...feature} index={index} />
      ))}
    </div>
  );
}

const Feature = ({
  title,
  description,
  icon,
  index,
}: {
  title: string;
  description: string;
  icon: React.ReactNode;
  index: number;
}) => {
  return (
    <div
      className={cn(
        "flex flex-col lg:border-r  py-10 relative group/feature dark:border-neutral-800",
        (index === 0 || index === 3) && "lg:border-l dark:border-neutral-800",
        index < 3 && "lg:border-b dark:border-neutral-800"
      )}
    >
      {index < 3 && (
        <div className="opacity-0 group-hover/feature:opacity-100 transition duration-200 absolute inset-0 h-full w-full bg-gradient-to-t from-neutral-100 dark:from-neutral-800 to-transparent pointer-events-none" />
      )}
      {index >= 3 && (
        <div className="opacity-0 group-hover/feature:opacity-100 transition duration-200 absolute inset-0 h-full w-full bg-gradient-to-b from-neutral-100 dark:from-neutral-800 to-transparent pointer-events-none" />
      )}
      <div className="mb-4 relative z-10 px-4 text-primary dark:text-neutral-400">
        {icon}
      </div>
      <div className="text-lg font-bold mb-2 relative z-10 px-4">
        <div className="absolute left-0 inset-y-0 h-6 group-hover/feature:h-8 w-1 rounded-tr-full rounded-br-full bg-neutral-300 dark:bg-neutral-700 group-hover/feature:bg-primary transition-all duration-200 origin-center" />
        <span className="group-hover/feature:translate-x-2 transition duration-200 inline-block text-neutral-800 dark:text-neutral-100">
          {title}
        </span>
      </div>
      <p className=" text-neutral-600 dark:text-neutral-300 max-w-[400px] relative z-10 px-4">
        {description}
      </p>
    </div>
  );
};
