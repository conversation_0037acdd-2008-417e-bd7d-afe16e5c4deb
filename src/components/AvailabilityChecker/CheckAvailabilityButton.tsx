"use client";

import { useState } from "react";
import { Button } from "../ui/button";
import { ArrowRight } from "lucide-react";
import CustomAvailabilityModal from "../CustomModal";
import AvailabilityChecker from ".";

interface CheckAvailabilityButtonProps {
  accommodationId: string;
}

const CheckAvailabilityButton = ({
  accommodationId,
}: CheckAvailabilityButtonProps) => {
  const [isOpen, setIsOpen] = useState(false);

  const onClose = () => {
    setIsOpen(false);
  };

  return (
    <div className="relative">
      <CustomAvailabilityModal isOpen={isOpen} onClose={onClose}>
        <AvailabilityChecker accommodationId={accommodationId} />
      </CustomAvailabilityModal>
      <Button
        type="button"
        onClick={() => setIsOpen(true)}
        disabled={!accommodationId}
        className="bg-primary hover:bg-primary/80 text-white rounded-full uppercase px-3 h-11 flex items-center gap-2 cursor-pointer"
      >
        Check Availability
        <span>
          <ArrowRight className="w-4 h-4" />
        </span>
      </Button>
    </div>
  );
};

export default CheckAvailabilityButton;
