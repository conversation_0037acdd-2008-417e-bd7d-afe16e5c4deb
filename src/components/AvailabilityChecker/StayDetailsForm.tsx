"use client";
import { useFormStep } from "@/hooks/use-form-step";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { format } from "date-fns";
import { anton } from "@/app/fonts";
import { CalendarIcon, BedDouble, Users, ArrowRight } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";

import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Input } from "@/components/ui/input";
import { useState } from "react";
import { toast } from "sonner";
import { confirmAvailability } from "@/app/actions";
import { zodResolver } from "@hookform/resolvers/zod";
import { cn } from "@/lib/utils";
import LoadingModal from "../Loaders/LoadingModal";
import { Card, CardContent } from "../ui/card";

const FormSchema = z
  .object({
    checkin: z.date({
      error: "A check-in date is required.",
    }),
    checkout: z.date({
      error: "A check-out date is required.",
    }),
    guests: z.coerce
      .number<number>()
      .min(1, "At least one guest is required.")
      .max(6, { error: "Maximum number of guests is 6" }),
    bedrooms: z.coerce
      .number<number>()
      .min(1, { error: "At least one bedroom is required." })
      .max(2, { error: "Maximum number is 2 bedrooms" }),
  })
  .refine((data) => data.checkout > data.checkin, {
    message: "Check-out date must be after check-in date.",
    path: ["checkout"],
  })
  .refine((data) => data.checkin < data.checkout, {
    message: "Check-in date must be before check-out date",
    path: ["checkin"],
  });

type StayDetailsSchema = z.infer<typeof FormSchema>;

const StayDetailsForm = ({ step, id }: { step: number; id: string }) => {
  const [isLoading, setIsLoading] = useState(false);
  const [initialGuestCount, setInitialGuestCount] = useState<number>(1);
  const [initialBedroomCount, setInitialBedroomCount] = useState<number>(1);
  const { setFormData, handleNext } = useFormStep({
    currentStep: step,
  });

  const form = useForm<StayDetailsSchema>({
    resolver: zodResolver(FormSchema),
    defaultValues: {
      guests: initialGuestCount,
      bedrooms: initialBedroomCount,
    },
  });

  async function onSubmit(data: StayDetailsSchema) {
    setIsLoading(true);
    try {
      const startDate = format(data.checkin, "yyyy-MM-dd");
      const endDate = format(data.checkout, "yyyy-MM-dd");
      const availability = await confirmAvailability(id, startDate, endDate);

      const stayDetails = {
        startDate,
        endDate,
        noOfGuests: data.guests,
        noOfBedrooms: data.bedrooms,
        isAvailable: availability.success,
        accommodationId: id,
      };

      handleNext(stayDetails);
    } catch (error) {
      console.error("Failed to fetch accommodation details:", error);
      toast.error("Error", {
        description: "Failed to fetch accommodation details. Please try again.",
      });
    } finally {
      setIsLoading(false);
    }
  }

  return (
    <div>
      <LoadingModal
        isOpen={isLoading}
        message="Checking Availability"
        text="Confirming accommodation availability"
      />
      <Card className="   px-0 border-none shadow-none rounded-none">
        <CardContent className="pt-12 px-0">
          <div className="text-center mb-10">
            <h4 className={`${anton.className} text-4xl text-primary mb-2`}>
              Stay Details
            </h4>
            <p>Secure your stay in just a few clicks</p>
          </div>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-8">
                <FormField
                  control={form.control}
                  name="checkin"
                  render={({ field }) => (
                    <FormItem className="flex flex-col">
                      <FormLabel>Check In</FormLabel>
                      <Popover>
                        <PopoverTrigger asChild>
                          <FormControl>
                            <Button
                              variant={"outline"}
                              className={cn(
                                "w-full pl-3 text-left font-normal bg-transparent rounded-none py-6",
                                !field.value && "text-muted-foreground",
                              )}
                            >
                              {field.value ? (
                                format(field.value, "PPP")
                              ) : (
                                <span>Pick a date</span>
                              )}
                              <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                            </Button>
                          </FormControl>
                        </PopoverTrigger>
                        <PopoverContent className="w-[364px] p-0" align="start">
                          <Calendar
                            className="w-[364px]"
                            mode="single"
                            selected={field.value}
                            onSelect={field.onChange}
                            disabled={(date) =>
                              date < new Date() ||
                              (form.getValues("checkout") &&
                                date > form.getValues("checkout"))
                            }
                            autoFocus
                          />
                        </PopoverContent>
                      </Popover>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="checkout"
                  render={({ field }) => (
                    <FormItem className="flex flex-col">
                      <FormLabel>Check Out</FormLabel>
                      <Popover>
                        <PopoverTrigger asChild>
                          <FormControl>
                            <Button
                              variant={"outline"}
                              className={cn(
                                "w-full pl-3 bg-transparent text-left font-normal rounded-none py-6",
                                !field.value && "text-muted-foreground",
                              )}
                            >
                              {field.value ? (
                                format(field.value, "PPP")
                              ) : (
                                <span>Pick a date</span>
                              )}
                              <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                            </Button>
                          </FormControl>
                        </PopoverTrigger>
                        <PopoverContent className="w-[364px] p-0" align="start">
                          <Calendar
                            className="w-[364px]"
                            mode="single"
                            selected={field.value}
                            onSelect={field.onChange}
                            disabled={(date) =>
                              date < (form.getValues("checkin") || new Date())
                            }
                            autoFocus
                          />
                        </PopoverContent>
                      </Popover>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-5">
                <FormField
                  control={form.control}
                  name="guests"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Number of Guests</FormLabel>
                      <FormControl>
                        <div className="relative">
                          <Users className="absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-muted-foreground" />
                          <Input
                            type="number"
                            placeholder="e.g. 2"
                            {...field}
                            className="pl-10 py-6 rounded-none"
                            onChange={(e) =>
                              field.onChange(parseInt(e.target.value, 10))
                            }
                          />
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="bedrooms"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Number of Bedrooms</FormLabel>
                      <FormControl>
                        <div className="relative">
                          <BedDouble className="absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-muted-foreground" />
                          <Input
                            type="number"
                            placeholder="e.g. 1"
                            {...field}
                            className="pl-10 py-6 rounded-none"
                            onChange={(e) =>
                              field.onChange(parseInt(e.target.value, 10))
                            }
                          />
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              <div className="w-full flex items-center justify-end">
                <Button
                  type="submit"
                  className="py-6 rounded-full px-4  cursor-pointer"
                >
                  Check Availability
                  <span>
                    <ArrowRight className="h-4 w-4" />
                  </span>
                </Button>
              </div>
            </form>
          </Form>
        </CardContent>
      </Card>
    </div>
  );
};

export default StayDetailsForm;
