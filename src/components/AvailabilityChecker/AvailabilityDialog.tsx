"use client";

import { useEffect, useMemo, useState } from "react";
import { AnimatePresence, motion } from "framer-motion";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import AccommodationCard from "@/components/AccommodationCard";
import DialogGuestDetailsForm from "./DialogGuestDetailsForm";
import type { IAccommodation } from "@/types";
import { ArrowLeft } from "lucide-react";

interface AvailabilityDialogProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  accommodations: IAccommodation[] | null;
  onSelect: (id: string) => void;
  title?: string;
  description?: string;
  formTitle?: string;
  formDescription?: string;
}

export default function AvailabilityDialog({
  isOpen,
  onOpenChange,
  accommodations,
  onSelect,
  title = "Available Accommodations",
  description = "Select an accommodation to proceed",
  formTitle = "Guest Details",
  formDescription = "Enter your details to continue",
}: AvailabilityDialogProps) {
  const hasItems = Boolean(accommodations && accommodations.length);
  const [view, setView] = useState<"list" | "form">("list");

  // Reset to list when dialog is closed or data changes
  useEffect(() => {
    if (!isOpen) setView("list");
  }, [isOpen]);
  useEffect(() => {
    setView("list");
  }, [accommodations]);

  const header = useMemo(() => {
    return view === "list"
      ? { title, description }
      : { title: formTitle, description: formDescription };
  }, [view, title, description, formTitle, formDescription]);

  const handleSelect = (id: string) => {
    onSelect(id);
    setView("form");
  };

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent
        className="mx-auto z-[1000] bg-slate-200 w-[95vw] md:max-w-[1220px] p-4 sm:p-6"
        style={{ height: 715.25 }}
      >
        <DialogHeader className="text-center!">
          <DialogTitle>{header.title}</DialogTitle>
          <DialogDescription>{header.description}</DialogDescription>
        </DialogHeader>

        <div className="relative h-[calc(715.25px-4rem)] sm:h-[calc(715.25px-5rem)] overflow-hidden">
          <motion.div
            className="flex h-full w-[200%]"
            animate={{ x: view === "list" ? "0%" : "-50%" }}
            transition={{ type: "tween", duration: 0.45, ease: "easeInOut" }}
          >
            {/* Pane 1: Accommodations List */}
            <div className="w-1/2 h-full overflow-auto pr-2">
              {hasItems ? (
                <div className="grid grid-cols-1 md:grid-cols-3 gap-2">
                  {accommodations!.map((acc, idx) => (
                    <AccommodationCard
                      key={idx}
                      acc={acc}
                      onSelect={handleSelect}
                    />
                  ))}
                </div>
              ) : (
                <div className="flex h-full items-center justify-center">
                  <p className="text-center text-sm text-slate-700 py-6">
                    All accommodations are occupied on these dates.
                  </p>
                </div>
              )}
            </div>

            {/* Pane 2: Form */}
            <div className="w-1/2 h-full overflow-auto pl-2">
              <div className="flex items-center justify-start mb-4"></div>
              <DialogGuestDetailsForm
                step={2}
                showBack
                onBack={() => setView("list")}
              />
            </div>
          </motion.div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
