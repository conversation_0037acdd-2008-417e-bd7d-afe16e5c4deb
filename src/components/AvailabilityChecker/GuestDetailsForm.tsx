"use client";

import { useFormStep } from "@/hooks/use-form-step";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { anton } from "@/app/fonts";
import { Button } from "@/components/ui/button";
import { useState } from "react";
import { toast } from "sonner";
import { Input } from "@/components/ui/input";

import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { allCountryNames } from "@/data/countries";
import { Combobox } from "@/components/ui/combo-box";
import { createPendingBooking } from "@/app/actions";
import LoadingModal from "../Loaders/LoadingModal";
import { Card, CardContent } from "../ui/card";
import { zodResolver } from "@hookform/resolvers/zod";
import { format } from "date-fns";
import { ArrowLeft, ArrowRight } from "lucide-react";

const FormSchema = z.object({
  firstName: z.string().min(2, {
    error: "First name must be at least 2 characters.",
  }),
  lastName: z.string().min(2, {
    error: "Last name must be at least 2 characters.",
  }),
  email: z.email({
    error: "Please enter a valid email address.",
  }),
  phoneNumber: z.string().min(10, {
    error: "Phone number must be at least 10 digits.",
  }),
  country: z.string({
    error: "Please select a country.",
  }),
});

type PersonalDetailsSchema = z.infer<typeof FormSchema>;

const GuestDetailsForm = ({ step }: { step: number }) => {
  const [isLoading, setIsLoading] = useState(false);

  const form = useForm<PersonalDetailsSchema>({
    resolver: zodResolver(FormSchema),
    defaultValues: {
      firstName: "",
      lastName: "",
      email: "",
      phoneNumber: "",
      country: "",
    },
  });

  const { setFormData, handleNext, setCurrentStep, getLatestState } =
    useFormStep({
      currentStep: step,
    });

  const latestState = getLatestState().formData;

  const startDate = latestState.startDate
    ? format(new Date(latestState.startDate), "yyyy-MM-dd")
    : "";

  const endDate = latestState.endDate
    ? format(new Date(latestState.endDate), "yyyy-MM-dd")
    : "";
  const id = latestState.accommodationId;
  const numberOfGuests = latestState.noOfGuests || 1;

  async function onSubmit(data: PersonalDetailsSchema) {
    setIsLoading(true);
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    let bookingDetails: any = {};

    try {
      bookingDetails = {
        accommodationId: id,
        guestFirstName: data.firstName,
        guestLastName: data.lastName,
        guestEmail: data.email,
        guestCountry: data.country,
        guestPhoneNumber: data.phoneNumber,
        startDate,
        endDate,
        numberOfGuests,
      };

      const currentValues = {
        guestFirstName: data.firstName,
        guestLastName: data.lastName,
        guestEmail: data.email,
        guestPhoneNumber: data.phoneNumber,
        guestCountry: data.country,
      };

      const response = await createPendingBooking(bookingDetails);

      if (!response) {
        console.error("Failed to create booking: No response from server.");
        toast.error("Failed to submit payment details. Please try again.");
        return;
      }
      if (!response.success) {
        toast.error(`An error occurred while booking: ${response.message}`);
        return;
      }

      const pendingBookingDetails = response.booking;

      const mergedValues = {
        ...currentValues,
        ...pendingBookingDetails,
      };

      handleNext(mergedValues);
    } catch (error) {
      console.error("Error creating payment details:", error);
      toast.error("Failed to create payment details. Please try again.");
    } finally {
      setIsLoading(false);
    }
  }
  const handleBackClick = () => {
    setFormData(latestState);
    setCurrentStep(step - 1);
  };
  return (
    <div>
      <LoadingModal
        isOpen={isLoading}
        message="Creating Payment Details"
        text="Calculating your payment details..."
      />
      <Card className="relative bg-white px-0 border-none shadow-none">
        <CardContent className="py-12 px-6">
          <div className="text-center mb-10">
            <h4 className={`${anton.className} text-4xl text-primary mb-2`}>
              Personal Details
            </h4>
            <p>Fill in your details to proceed</p>
          </div>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-5">
                <FormField
                  control={form.control}
                  name="firstName"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>First Name</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="e.g. John"
                          {...field}
                          className="py-6 rounded-none"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="lastName"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Last Name</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="e.g. Doe"
                          {...field}
                          className="py-6 rounded-none"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-5 ">
                <FormField
                  control={form.control}
                  name="phoneNumber"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Phone Number</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="e.g. +1234567890"
                          {...field}
                          className="py-6 rounded-none"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="country"
                  render={({ field }) => (
                    <FormItem className="flex flex-col">
                      <FormLabel>Country</FormLabel>
                      <Combobox
                        options={allCountryNames.map((country) => ({
                          value: country,
                          label: country,
                        }))}
                        value={field.value}
                        onChange={field.onChange}
                        placeholder="Select country..."
                        searchPlaceholder="Search country..."
                        notFoundText="No country found."
                      />
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              <FormField
                control={form.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Email</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="e.g. <EMAIL>"
                        {...field}
                        className="py-6 rounded-none"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <div className="w-full flex items-center justify-between">
                <Button
                  onClick={handleBackClick}
                  type="button"
                  variant={"outline"}
                  className="py-6 rounded-full px-4 font-semibold cursor-pointer"
                >
                  <span>
                    <ArrowLeft className="h-4 w-4" />
                  </span>
                  Back to Availability
                </Button>

                <Button
                  type="submit"
                  className="py-6 rounded-full px-4 font-semibold cursor-pointer"
                >
                  Proceed to Payment
                  <span>
                    <ArrowRight className="h-4 w-4" />
                  </span>
                </Button>
              </div>
            </form>
          </Form>
        </CardContent>
      </Card>
    </div>
  );
};

export default GuestDetailsForm;
