"use client";

import { useEffect, useState } from "react";
import { useFormStore } from "@/lib/store";
import { useFormStep } from "@/hooks/use-form-step";
import SuccessAvailabilityCard from "../ui/success-availability-card";
import UnavailableCard from "../ui/unavailability-card";

const AccommodationStatus = ({ step }: { step: number }) => {
  const [isSuccess, setIsSuccess] = useState<boolean | undefined>(undefined);
  const formData = useFormStore((state) => state.formData);

  const { getLatestState, handleNext, setCurrentStep, setFormData } =
    useFormStep({
      currentStep: step,
    });

  useEffect(() => {
    const latestState = getLatestState();
    if (latestState) {
      setIsSuccess(latestState.formData.isAvailable);
    }
  }, [getLatestState]);

  const handleBack = () => {
    const availabilityData = {
      isAvailable: false,
    };
    setFormData(availabilityData);
    setCurrentStep(step - 1);
  };

  const onProceedToBook = () => {
    const availabilityData = {
      isAvailable: true,
    };

    handleNext(availabilityData);
  };

  // Render success or error message based on the availability
  if (isSuccess === false) {
    return <UnavailableCard onGoBack={handleBack} />;
  } else if (isSuccess === true) {
    return (
      <SuccessAvailabilityCard
        onProceedToBook={onProceedToBook}
        onBack={handleBack}
      />
    );
  }
};

export default AccommodationStatus;
