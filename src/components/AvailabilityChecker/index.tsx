"use client";
import { useFormStore } from "@/lib/store";
import StayDetailsForm from "./StayDetailsForm";
import { FormLayout } from "../layout/form-layout";
import AccommodationStatus from "./AccommodationStatus";
import GuestDetailsForm from "./GuestDetailsForm";
import PaymentCard from "../PaymentCard";

interface AvailabilityCheckerProps {
  accommodationId: string;
}

const AvailabilityChecker = ({ accommodationId }: AvailabilityCheckerProps) => {
  const currentStep = useFormStore((state) => state.currentStep);

  const renderStep = () => {
    switch (currentStep) {
      case 1:
        return <StayDetailsForm step={1} id={accommodationId} />;
      case 2:
        return <AccommodationStatus step={2} />;
      case 3:
        return <GuestDetailsForm step={3} />;
      case 4:
        return <PaymentCard step={4} />;
      default:
        return <div>Step {currentStep} coming soon...</div>;
    }
  };
  return <FormLayout>{renderStep()}</FormLayout>;
};

export default AvailabilityChecker;
