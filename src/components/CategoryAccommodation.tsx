import { <PERSON><PERSON><PERSON>, <PERSON> } from "lucide-react";
import { Badge } from "./ui/badge";
import { Card } from "./ui/card";
import { Button } from "./ui/button";
import { formatToKsh } from "@/lib/utils";
import Image from "next/image";
import Link from "next/link";

interface AccommodationProps {
  id: string;
  name: string;
  pricePerNight: number;
  summary: string;
  bedrooms: number;
  maxCapacity: number;
  category: string;
  images: { url: string }[];
}

const CategoryAccommodation = ({
  name,
  summary,
  pricePerNight,
  images,
  bedrooms,
  maxCapacity,
  category,
}: AccommodationProps) => {
  return (
    <Card className="w-full max-w-md bg-white rounded-3xl overflow-hidden shadow-lg h-4/5 px-2 py-2">
      {/* Image section */}
      <div className="relative rounded-2xl bg-black/30 h-[355px] w-full">
        <Image
          src={images[0].url}
          fill
          alt={name}
          className="object-cover rounded-2xl"
        />
        <div className="absolute top-4 left-4 flex gap-2">
          <Badge className="bg-white/20 backdrop-blur-sm text-white border-white/30 hover:bg-white/20">
            {bedrooms} bedrooms
          </Badge>
          <Badge className="bg-white/20 backdrop-blur-sm text-white border-white/30 hover:bg-white/20">
            {maxCapacity} guests
          </Badge>
        </div>
        <div className="absolute top-4 right-4 flex items-center gap-1 text-white">
          <Star className="w-4 h-4 fill-white" />
          <span className="text-sm font-medium">4.8</span>
        </div>
      </div>

      {/* Content section */}
      <div className="px-4 pb-6 ">
        <div className="flex justify-between items-start mb-4">
          <div>
            <h2 className="text-2xl font-semibold text-gray-900 mb-2">
              {name}
            </h2>
          </div>
          <Link
            href={`/accommodations/${category}/${name}`}
            className="bg-transparent text-gray-700 border-gray-400/60 border text-xs hover:bg-gray-100 px-3 py-1"
          >
            View Details
          </Link>
        </div>

        <p className="text-gray-600 text-sm mb-6 leading-relaxed">{summary}</p>

        <div className="flex justify-between items-center">
          <div className="text-xl font-semibold text-gray-900">
            {formatToKsh(pricePerNight)}
            <span className="text-base font-normal text-gray-600">/ night</span>
          </div>
          <Button className="bg-primary hover:bg-primary/80 text-white rounded-full uppercase px-3 h-11 flex items-center gap-2">
            Check Availability
            <ArrowRight className="w-4 h-4" />
          </Button>
        </div>
      </div>
    </Card>
  );
};

export default CategoryAccommodation;
