import { poppins } from "@/app/fonts";
import { IContactInfo } from "@/data/contactInfo";
import Image from "next/image";

const ContactCard = ({ icon, title, text, link }: IContactInfo) => {
  return (
    <a
      href={link}
      className="px-[2.4rem] md:px-[18px] md:py-[18px] md:max-w-[31.2rem] bg-white shadow-lg lg:w-[calc(50%-0.6rem)]"
    >
      <div className="flex items-center mb-1">
        <Image
          src={icon}
          width={32}
          height={32}
          alt={title}
          className="object-cover"
        />
        <h3 className="font-semibold ml-[0.8rem] text-xl mt-0">{title}</h3>
      </div>
      <p
        className={`${poppins.className} font-light mb-[2.5rem] text-[1.2rem] md:text-[14px]`}
      >
        {text}
      </p>
      <div>
        <p
          className={`${poppins.className} font-light text-sm underline underline-offset-2`}
        >
          Get in Touch
        </p>
      </div>
    </a>
  );
};

export default ContactCard;
