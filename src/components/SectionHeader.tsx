import { cn } from "@/lib/utils";
import * as React from "react";

type SectionHeaderProps = {
  title: string;
  text?: string;
};

const SectionHeader = ({
  title,
  text,
  className,
  ...props
}: React.ComponentProps<"div"> & SectionHeaderProps) => {
  return (
    <div
      className={cn(`flex flex-col max-w-[540px] mx-auto`, className)}
      {...props}
    >
      <h4 className="text-primary text-xl sm:text-2xl md:text-3xl lg:text-4xl xl:text-5xl font-bold tracking-tighter">
        {title}
      </h4>
      <p className="mt-5 opacity-75 text-center">{text}</p>
    </div>
  );
};

export default SectionHeader;
