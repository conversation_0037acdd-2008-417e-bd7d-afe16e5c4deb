"use client";

import { BookingRoutes } from "@/types";
import clsx from "clsx";
import Link from "next/link";
import { Check } from "lucide-react";
import { usePathname, useSearchParams } from "next/navigation";
import path from "path";
import { useEffect, useState } from "react";

const steps = [
  {
    title: "Select Dates & Guests",
    route: "step-one",
    link: BookingRoutes.GUEST_DATES,
  },
  {
    title: "Choose Your Room",
    route: "step-two",
    link: BookingRoutes.ACCOMODATION_SELECTION,
  },
  {
    title: "Guest Information",
    route: "step-three",
    link: BookingRoutes.GUEST_INFO,
  },
  {
    title: "Payment",
    route: "payment",
    link: BookingRoutes.PAYMENT_ROUTE,
  },
];

const StepNavigation = () => {
  const pathName = usePathname();
  const searchParams = useSearchParams();
  const currentPath = path.basename(pathName);
  const [currentStep, setCurrentStep] = useState(0);
  const [completedSteps, setCompletedSteps] = useState<number[]>([]);

  useEffect(() => {
    setCurrentStep(steps.findIndex((step) => step.route === currentPath));
  }, [currentPath]);

  useEffect(() => {
    const completed = searchParams.get("completed");
    if (completed) {
      setCompletedSteps((prev) => [
        ...prev,
        ...completed.split(",").map(Number),
      ]);
    }
  }, [searchParams]);
  return (
    <div className="mb-8  border-b-[0.8px] border-primary/45 ">
      {/* List of form steps */}
      <div className="relative flex max-w-4xl pb-10 mx-auto">
        {steps.map((step, i) => (
          <Link
            href={step.link}
            key={step.link}
            className="group z-20 flex flex-1 flex-col items-center gap-3 text-base"
            prefetch={true}
          >
            <span
              className={clsx(
                "flex h-7 w-7 items-center justify-center rounded-full border  text-sm  transition-colors duration-200  lg:h-10 lg:w-10 lg:text-base",
                {
                  "border-none bg-primary text-white group-hover:border-none group-hover:text-white":
                    currentStep === i,
                  "border-white/75 bg-gray-900 group-hover:border-white group-hover:text-white text-white/75":
                    currentStep !== i,
                  "bg-green-500 border-none text-white":
                    completedSteps.includes(i),
                },
              )}
            >
              {completedSteps.includes(i) ? <Check /> : i + 1}
            </span>
            <span
              className={clsx(
                " text-black text-sm text-center transition-colors duration-200 group-hover:text-primary",
                {
                  "font-light": currentPath !== step.route,
                  " text-black/75": currentPath === step.route,
                },
              )}
            >
              {step.title}
            </span>
          </Link>
        ))}
        <div className="absolute top-4 flex h-1 inset-x-[12.5%] border-b border-primary border-dashed" />
      </div>
    </div>
  );
};

export default StepNavigation;
