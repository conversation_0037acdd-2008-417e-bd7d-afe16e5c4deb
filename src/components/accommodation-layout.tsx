"use client";

import { anton } from "@/app/fonts";
import { formatToKsh } from "@/lib/utils";
import { useState } from "react";
import CheckAvailabilityButton from "./AvailabilityChecker/CheckAvailabilityButton";
import { ArrowRight, BedDouble, UsersRound } from "lucide-react";
import { Button } from "./ui/button";

const AccommodationLayout = () => {
  const [hoveredImage, setHoveredImage] = useState<string | null>(null);

  return (
    <div className="w-full bg-gray-50  h-[450px]">
      <div className="max-w-7xl mx-auto h-full">
        {/* Two column grid */}
        <div className="grid grid-cols-12 gap-6">
          {/* Left Column - Accommodation Info */}
          <div className="col-span-4 bg-white h-[450px] rounded-lg p-4 flex flex-col justify-between shadow-sm">
            <div className="">
              {/* Title */}
              <h2
                className={`text-2xl font-bold text-gray-800 mb-12  ${anton.className}`}
              >
                C2
                <br />
                <span className="text-primary uppercase">
                  Executive Accommodation
                </span>
              </h2>

              {/* Description */}
              <p className="text-gray-600 mb-4 text-wrap">
                This luxury accommodation offers fully en-suite rooms, providing
                a private and comfortable space for guests. The rooms feature a
                spacious lounge with a coffee table, allowing guests to enjoy a
                cup of coffee or tea while spending time together or
                relaxing.{" "}
              </p>

              {/* Features */}
              <div className="grid grid-cols-3 gap-4 text-xs text-gray-600 mb-6">
                <div>
                  <div className="font-medium text-gray-800 bg-neutral-200 py-2 rounded-full flex gap-2 items-center justify-center">
                    <span>
                      <UsersRound className="h-4 w-4" />
                    </span>
                    <span>2 guests</span>
                  </div>
                </div>
                <div>
                  <div className="font-medium text-gray-800 bg-neutral-200 py-2 rounded-full flex gap-2 items-center justify-center">
                    <span>
                      <BedDouble className="h-4 w-4" />
                    </span>
                    <span>2 Beds</span>
                  </div>
                </div>{" "}
                <div></div>
              </div>
            </div>

            {/* Price and Buttons */}
            <div>
              <div className="mb-6">
                <div className="text-xs text-gray-500 mb-1">
                  Price per night
                </div>
                <div className="text-2xl font-bold text-gray-800">
                  <p className={`${anton.className} uppercase`}>
                    {formatToKsh(15000)}
                  </p>
                </div>
              </div>

              <div className="flex gap-3">
                <CheckAvailabilityButton accommodationId="xsertyu" />
                <Button
                  type="button"
                  variant={"outline"}
                  className=" hover:bg-black text-neutral-600 hover:text-white transition-all duration-300 rounded-full uppercase px-3 h-11 flex items-center gap-2 cursor-pointer"
                >
                  View Details
                  <span>
                    <ArrowRight className="w-4 h-4" />
                  </span>
                </Button>
              </div>
            </div>
          </div>

          {/* Right Column - Images */}
          <div className="col-span-8 flex gap-2 h-96">
            {/* Large main image */}
            <div
              className={`rounded-lg h-[450px] overflow-hidden transition-all duration-500 ease-in-out ${
                hoveredImage ? "flex-[1_1_0%]" : "flex-[4_1_0%]"
              }`}
            >
              <img
                src="https://images.unsplash.com/photo-1631049307264-da0ec9d70304?w=800&h=600&fit=crop"
                alt="Modern hotel room with large windows"
                className="w-full h-full object-cover rounded-lg"
              />
            </div>

            {/* First smaller image */}
            <div
              className={`rounded-lg h-[450px] relative overflow-hidden transition-all duration-500 ease-in-out ${
                hoveredImage === "room" ? "flex-[4_1_0%]" : "flex-[1_1_0%]"
              }`}
              onMouseEnter={() => setHoveredImage("room")}
              onMouseLeave={() => setHoveredImage(null)}
            >
              <img
                src="https://images.unsplash.com/photo-1578683010236-d716f9a3f461?w=400&h=600&fit=crop"
                alt="Hotel bathroom"
                className="w-full h-full object-cover rounded-lg"
              />
              <div className="absolute bottom-2 right-2 bg-black bg-opacity-50 text-white px-2 py-1 rounded text-xs">
                KITCHEN
              </div>
            </div>

            {/* Second smaller image */}
            <div
              className={`rounded-lg h-[450px] relative overflow-hidden transition-all duration-500 ease-in-out ${
                hoveredImage === "bathroom" ? "flex-[4_1_0%]" : "flex-[1_1_0%]"
              }`}
              onMouseEnter={() => setHoveredImage("bathroom")}
              onMouseLeave={() => setHoveredImage(null)}
            >
              <img
                src="https://images.unsplash.com/photo-1584132967334-10e028bd69f7?w=400&h=600&fit=crop"
                alt="Hotel bathroom detail"
                className="w-full h-full object-cover rounded-lg"
              />
              <div className="absolute bottom-2 right-2 bg-black bg-opacity-50 text-white px-2 py-1 rounded text-xs">
                BEDROOM
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AccommodationLayout;
