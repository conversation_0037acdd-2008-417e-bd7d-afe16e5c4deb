"use client";

import { memo, useState, useMemo, useCallback } from "react";

interface ImageType {
  id: string;
  imageUrl: string;
  category: string;
}

const categories = [
  "All",
  "Living-Room",
  "Bedrooms",
  "Kitchen",
  "Terrace",
  "Washroom",
  "Exterior",
] as const;

type Category = (typeof categories)[number];

const GalleryComponent = ({ images }: { images: ImageType[] }) => {
  const [activeTab, setActiveTab] = useState(0);

  const [activeCategory, setActiveCategory] = useState<Category>("All");

  const filteredImages = useMemo(() => {
    if (activeCategory === "All") return images;
    const lower = activeCategory.toLowerCase();
    return images.filter((img) => img.category.toLowerCase() === lower);
  }, [images, activeCategory]);

  const handleCategoryClicked = useCallback(
    (index: number, category: Category) => {
      setActiveTab(index);
      setActiveCategory(category);
    },
    []
  );
  return (
    <div>
      <div className="w-full flex items-center justify-center py-6">
        <ul className="flex flex-wrap gap-5 border-b-[0.8px] border-primary/30 pb-3 text-sm md:gap-0 md:space-x-7">
          {categories.map((category, index) => (
            <li
              key={index}
              className={`min-w-max ${
                index === activeTab ? "font-bold text-primary" : ""
              }`}
            >
              <button onClick={() => handleCategoryClicked(index, category)}>
                {category}
              </button>
            </li>
          ))}
        </ul>
      </div>

      <div className="columns-1  md:columns-2 lg:columns-3">
        {filteredImages.map((image, index) => (
          <div key={index} className="mb-4 break-inside-avoid">
            <img
              src={image.imageUrl}
              alt="image"
              className="h-[300px] md:h-full w-full rounded-lg object-cover"
            />
          </div>
        ))}
      </div>
    </div>
  );
};

export default memo(GalleryComponent);
