"use client";

import { useState, useEffect, useMemo } from "react";
import Image from "next/image";

import dynamic from "next/dynamic";
// dynamically import react-slick only on the client to reduce initial JS
const Slider = dynamic(() => import("react-slick"), { ssr: false });
import "slick-carousel/slick/slick.css";
import "slick-carousel/slick/slick-theme.css";

import Preloader from "../Loaders/Preloader";
import { anton } from "@/app/fonts";
import { useHero } from "@/context/HeroContext";

const allMedia = [
  "/hero/hero-1.webp",
  "/hero/hero-2.webp",
  "/hero/hero-3.webp",
];

const HeroClient = () => {
  const [imagesLoaded, setImagesLoaded] = useState(
    Array(allMedia.length).fill(false) as boolean[],
  );
  const { allImagesLoaded, setAllImagesLoaded } = useHero();

  const handleImageLoaded = (index: number) => {
    setImagesLoaded((prev) => {
      if (prev[index]) return prev; // avoid state churn
      const next = [...prev];
      next[index] = true;
      return next;
    });
  };

  useEffect(() => {
    if (imagesLoaded.every(Boolean)) {
      setAllImagesLoaded(true);
    }
  }, [imagesLoaded, setAllImagesLoaded]);

  useEffect(() => {
    document.body.style.overflow = allImagesLoaded ? "auto" : "hidden";
    return () => {
      document.body.style.overflow = "auto";
    };
  }, [allImagesLoaded]);

  const settings = useMemo(
    () => ({
      infinite: true,
      speed: 9000,
      autoplaySpeed: 8000,
      slidesToShow: 1,
      autoplay: true,
      slidesToScroll: 1,
      cssEase: "linear",
      fade: true,
      arrows: false,
      pauseOnHover: false,
      swipeToSlide: true,
    }),
    [],
  );

  return (
    <div className="relative h-full w-full rounded-lg">
      <Slider
        {...settings}
        className="h-full [&_.slick-list]:h-full [&_.slick-track]:h-full [&_.slick-slide]:h-full [&_.slick-slide>div]:h-full rounded-lg"
      >
        {allMedia.map((image, idx) => (
          <div className="relative w-full h-full rounded-lg" key={idx}>
            <div className="relative h-full w-full">
              <Image
                fill
                className="object-cover h-full rounded-lg"
                priority={idx === 0}
                src={image}
                alt="image"
                onLoad={() => handleImageLoaded(idx)}
              />
            </div>
          </div>
        ))}
      </Slider>
      {!allImagesLoaded ? (
        <div className="fixed h-screen left-0 top-0 w-full z-[1000]">
          <Preloader />
        </div>
      ) : (
        <>
          <div className="absolute h-full rounded-lg  inset-0 z-30 bg-black/45 " />
          <div className="absolute  h-full  inset-0 z-40 max-w-[700px] px-5 text-white">
            <div className="flex flex-col h-full justify-center gap-6 md:gap-8">
              <div className="flex flex-col space-y-3">
                <p className="font-bold text-amber-300">Diani&apos;s Escape</p>
                <h4
                  className={`${anton.className} flex flex-col text-7xl leading-[1] tracking-tight`}
                >
                  Experience <span>Serenity at</span>
                  <span>Silent Palms Villa</span>
                </h4>
              </div>
            </div>
          </div>
        </>
      )}
    </div>
  );
};

export default HeroClient;
