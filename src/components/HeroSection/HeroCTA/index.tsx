import Link from "next/link";

const HeroCTA = () => {
  return (
    <div>
      <Link
        href="/reservation"
        prefetch={false}
        className="relative inline-block px-4 py-3 md:px-5 md:py-3 font-medium  group"
      >
        <span className="absolute inset-0 w-full h-full rounded-lg transition duration-200 ease-out transform translate-x-1 translate-y-1 bg-green-600 group-hover:-translate-x-0 group-hover:-translate-y-0"></span>
        <span className="absolute inset-0 w-full h-full rounded-lg  bg-white border-2 border-primary group-hover:bg-green-700 group-hover:border-2 group-hover:border-green-700"></span>
        <span className="relative flex justify-center items-center  text-xs md:text-base text-black group-hover:text-white uppercase">
          Book Now
          <svg
            aria-hidden="true"
            className="ml-2 -mr-1 w-4 h-4"
            fill="currentColor"
            viewBox="0 0 20 20"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              fillRule="evenodd"
              d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z"
              clipRule="evenodd"
            ></path>
          </svg>
        </span>
      </Link>
    </div>
  );
};

export default HeroCTA;
