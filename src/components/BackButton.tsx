"use client";

import { ArrowLeft } from "lucide-react";
import { But<PERSON> } from "./ui/button";
import { useRouter } from "next/navigation";

const BackButton = () => {
  const router = useRouter();
  return (
    <Button
      className="flex items-center gap-2 text-primary bg-transparent border-[0.8px] border-primary rounded-full h-12 px-10 hover:bg-primary hover:text-white transition-colors duration-200 cursor-pointer"
      onClick={() => router.back()}
    >
      <ArrowLeft className="w-4 h-4" />
      Back To all Accommodations
    </Button>
  );
};

export default BackButton;
