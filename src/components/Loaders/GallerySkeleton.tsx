const SkeletonCard = ({ height }: { height: string }) => (
  <div className="mb-4 break-inside-avoid">
    <div
      className="w-full rounded-lg bg-gray-300 animate-pulse"
      style={{ height }}
    ></div>
  </div>
);

const GallerySkeleton = () => {
  const heights = [
    "250px",
    "300px",
    "350px",
    "200px",
    "400px",
    "280px",
    "320px",
    "220px",
    "380px",
  ];

  return (
    <div>
      <div className="w-full flex items-center justify-center py-6">
        <ul className="flex flex-wrap gap-5 border-b-[0.8px] border-primary/30 pb-3 text-sm md:gap-0 md:space-x-7">
          {Array.from({ length: 7 }).map((_, index) => (
            <li key={index} className="min-w-max">
              <div className="h-6 w-20 bg-gray-300 rounded animate-pulse"></div>
            </li>
          ))}
        </ul>
      </div>

      <div className="columns-1 md:columns-2 lg:columns-3">
        {heights.map((height, index) => (
          <SkeletonCard key={index} height={height} />
        ))}
      </div>
    </div>
  );
};

export default GallerySkeleton;

