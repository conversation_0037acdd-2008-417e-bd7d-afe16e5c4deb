"use client";

import { useEffect } from "react";
import PulseSpinner from "../PulseSpinner";
import { anton } from "@/app/fonts";

interface LoadingModalProps {
  isOpen: boolean;
  message?: string;
  text?: string;
}

const LoadingModal = ({ isOpen, message, text }: LoadingModalProps) => {
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = "hidden";
    } else {
      document.body.style.overflow = "auto";
    }

    return () => {
      document.body.style.overflow = "auto";
    };
  }, [isOpen]);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-white z-[3000] flex items-center justify-center p-4">
      <div className=" max-h-[90vh] overflow-auto w-full max-w-2xl flex items-center justify-center">
        <div className="flex flex-col  space-y-12">
          <PulseSpinner />
          <div className="flex flex-col space-y-1 text-center">
            <p className={`font-semibold text-3xl ${anton.className}`}>
              {message}
            </p>
            <p>{text}</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LoadingModal;
