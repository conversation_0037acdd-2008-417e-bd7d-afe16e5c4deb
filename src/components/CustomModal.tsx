import React, { useEffect } from "react";

interface CustomAvailabilityModalProps {
  isOpen: boolean;
  onClose: () => void;
  children: React.ReactNode;
}

const CustomAvailabilityModal = ({
  isOpen,
  onClose,
  children,
}: CustomAvailabilityModalProps) => {
  // Prevent background scrolling when modal is open
  useEffect(() => {
    if (isOpen) {
      document.body.classList.add("modal-open");
    } else {
      document.body.classList.remove("modal-open");
    }

    // Cleanup function
    return () => {
      document.body.classList.remove("modal-open");
    };
  }, [isOpen]);

  // Close modal on backdrop click
  const handleBackdropClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) onClose();
  };

  if (!isOpen) return null;

  return (
    <div
      className="fixed inset-0 bg-black/80 z-[300] flex items-center justify-center"
      onClick={handleBackdropClick}
    >
      <div className=" bg-white w-full max-w-4xl px-4 h-fit rounded-md">
        {children}
      </div>
    </div>
  );
};

export default CustomAvailabilityModal;
