"use client";
import { useReservation } from "@/context/ReservationContext";
import { hotels } from "@/data/hotels";
import HotelCard from "@/components/HotelCard";
import { areIntervalsOverlapping, parseISO } from "date-fns";

const ReservationCard = () => {
  const { reservationData } = useReservation();

  if (!reservationData) {
    return <div>Loading...</div>;
  }

  const { startDate, endDate } = reservationData;
  const selectedInterval = {
    start: parseISO(startDate),
    end: parseISO(endDate),
  };

  const availableHotels = hotels.filter((hotel) => {
    return !hotel.bookedRanges.some((range) => {
      return areIntervalsOverlapping(
        selectedInterval,
        { start: range.from, end: range.to },
        { inclusive: true },
      );
    });
  });

  const unavailableHotels = hotels.filter((hotel) => {
    return hotel.bookedRanges.some((range) => {
      return areIntervalsOverlapping(
        selectedInterval,
        { start: range.from, end: range.to },
        { inclusive: true },
      );
    });
  });

  return (
    <div className="px-4 py-2">
      <h1 className="text-2xl font-bold mb-4">Available Hotels</h1>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {availableHotels.map((hotel, index) => (
          <HotelCard key={index} {...hotel} isAvailable={true} />
        ))}
      </div>
      <h1 className="text-2xl font-bold mt-8 mb-4">Unavailable Hotels</h1>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {unavailableHotels.map((hotel, index) => (
          <HotelCard key={index} {...hotel} isAvailable={false} />
        ))}
      </div>
    </div>
  );
};

export default ReservationCard;
