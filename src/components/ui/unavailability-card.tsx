"use client";

import { useState, useEffect, useMemo } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { ArrowLeft, Calendar } from "lucide-react";
import { anton } from "@/app/fonts";

interface UnavailableCardProps {
  onGoBack: () => void;
}

const UnavailableCard = ({ onGoBack }: UnavailableCardProps) => {
  const [showRaindrops, setShowRaindrops] = useState(false);

  const handleBackClick = () => onGoBack();

  useEffect(() => {
    setShowRaindrops(true);
    const timer = setTimeout(() => setShowRaindrops(false), 4000);
    return () => clearTimeout(timer);
  }, []);

  return (
    <div className="relative overflow-hidden max-w-7xl mx-auto">
      {/* Raindrop Animation */}
      <div className="absolute inset-0 pointer-events-none z-10 overflow-hidden">
        <Raindrop showRaindrops={showRaindrops} />
      </div>

      <Card className="relative bg-white border-none shadow-none">
        <CardContent className="p-12 text-center space-y-8">
          {/* Unavailable Icon */}
          <div className="flex justify-center mb-6">
            <div className="w-14 h-14 rounded-full flex items-center justify-center shadow-xl bg-red-500">
              <svg
                className="w-7 h-7 text-white"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={3}
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </div>
          </div>

          {/* Title Section */}
          <div className="space-y-4">
            <h1
              className={` ${anton.className} text-4xl font-bold tracking-tight text-gray-700 mb-6`}
            >
              Not Available
            </h1>
            <div className="flex justify-center space-x-2">
              <span
                className="text-2xl animate-bounce"
                style={{ animationDelay: "0s" }}
              >
                😔
              </span>
              <span
                className="text-2xl animate-bounce"
                style={{ animationDelay: "0.2s" }}
              >
                💔
              </span>
              <span
                className="text-2xl animate-bounce"
                style={{ animationDelay: "0.4s" }}
              >
                😞
              </span>
              <span
                className="text-2xl animate-bounce"
                style={{ animationDelay: "0.6s" }}
              >
                😢
              </span>
              <span
                className="text-2xl animate-bounce"
                style={{ animationDelay: "0.8s" }}
              >
                🌧️
              </span>
            </div>
          </div>

          {/* Disappointment Message */}
          <div className="max-w-2xl mx-auto">
            <p className="text-gray-600 text-xl font-medium leading-relaxed">
              Unfortunately, your selected dates are not available. But
              don&apos;t worry! There are plenty of other amazing dates and
              options waiting for you.
            </p>
          </div>

          {/* Decorative Elements */}
          <div className="flex justify-center space-x-8 py-4">
            <div className="flex flex-col items-center space-y-2">
              <div className="w-4 h-4 rounded-full bg-gray-400 animate-pulse"></div>
              <div className="w-2 h-2 rounded-full bg-gray-300"></div>
            </div>
            <div className="flex flex-col items-center space-y-2">
              <div
                className="w-4 h-4 rounded-full bg-slate-400 animate-pulse"
                style={{ animationDelay: "0.5s" }}
              ></div>
              <div className="w-2 h-2 rounded-full bg-slate-300"></div>
            </div>
            <div className="flex flex-col items-center space-y-2">
              <div
                className="w-4 h-4 rounded-full bg-blue-400 animate-pulse"
                style={{ animationDelay: "1s" }}
              ></div>
              <div className="w-2 h-2 rounded-full bg-blue-300"></div>
            </div>
            <div className="flex flex-col items-center space-y-2">
              <div
                className="w-4 h-4 rounded-full bg-gray-500 animate-pulse"
                style={{ animationDelay: "1.5s" }}
              ></div>
              <div className="w-2 h-2 rounded-full bg-gray-400"></div>
            </div>
          </div>

          {/* Go Back Button */}
          <div className="pt-4">
            <Button
              onClick={handleBackClick}
              className="text-white font-bold py-6 px-16 rounded-full shadow-xl hover:shadow-2xl transform transition-all duration-200 bg-primary cursor-pointer"
            >
              <ArrowLeft className="w-8 h-8 mr-3" />
              Go Back and Select Other Dates
              <Calendar className="w-8 h-8 ml-3" />
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default UnavailableCard;

const Raindrop = ({ showRaindrops }: { showRaindrops: boolean }) => {
  const pieces = useMemo(() => {
    const colors = [
      "#94a3b8", // Slate
      "#64748b", // Slate darker
      "#475569", // Slate darkest
      "#6b7280", // Gray
      "#9ca3af",
    ];
    return Array.from({ length: 40 }).map((_, i) => ({
      id: i,
      left: `${Math.random() * 100}%`,
      backgroundColor: colors[Math.floor(Math.random() * colors.length)],
      animationDelay: `${Math.random() * 2.5}s`,
      animationDuration: `${3 + Math.random() * 2}s`,
      borderRadius: Math.random() > 0.5 ? "50%" : "0%",
    }));
  }, []);
  return (
    <>
      {pieces.map((p) => (
        <div
          key={p.id}
          className={`absolute w-4 h-4 opacity-0 ${showRaindrops ? "animate-raindrop" : ""}`}
          style={p}
        />
      ))}
    </>
  );
};
