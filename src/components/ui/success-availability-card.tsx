"use client";

import { useState, useEffect, useMemo } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { ArrowLeft, ArrowRight } from "lucide-react";
import { anton } from "@/app/fonts";

interface SuccessCardProps {
  onProceedToBook: () => void;
  onBack: () => void;
}

const SuccessAvailabilityCard = ({
  onProceedToBook,
  onBack,
}: SuccessCardProps) => {
  const [showConfetti, setShowConfetti] = useState(false);
  const handleNext = () => onProceedToBook();
  const handleBackClick = () => onBack();

  useEffect(() => {
    setShowConfetti(true);
    const timer = setTimeout(() => setShowConfetti(false), 4000);
    return () => clearTimeout(timer);
  }, []);

  return (
    <div className="relative overflow-hidden max-w-7xl mx-auto">
      {/* Confetti Animation */}
      <div className="absolute inset-0 pointer-events-none z-10 overflow-hidden">
        <ConfettiPieces showConfetti={showConfetti} />
      </div>

      <Card className="relative bg-white border-none shadow-none">
        <CardContent className="p-12 text-center space-y-8">
          {/* Success Icon */}
          <div className="flex justify-center mb-6">
            <div className="w-14 h-14 rounded-full flex items-center justify-center shadow-lg bg-primary">
              <svg
                className="w-7 h-7 text-white"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={3}
                  d="M5 13l4 4L19 7"
                />
              </svg>
            </div>
          </div>

          {/* Title Section */}
          <div>
            <h1
              className={`${anton.className} text-4xl font-bold tracking-tight text-primary mb-6 `}
            >
              Accommodation is Available
            </h1>
            <div className="flex justify-center space-x-2">
              <span
                className="text-2xl animate-bounce"
                style={{ animationDelay: "0s" }}
              >
                🎉
              </span>
              <span
                className="text-2xl animate-bounce"
                style={{ animationDelay: "0.1s" }}
              >
                ✨
              </span>
              <span
                className="text-2xl animate-bounce"
                style={{ animationDelay: "0.2s" }}
              >
                🎊
              </span>
              <span
                className="text-2xl animate-bounce"
                style={{ animationDelay: "0.3s" }}
              >
                🥳
              </span>
              <span
                className="text-2xl animate-bounce"
                style={{ animationDelay: "0.4s" }}
              >
                🎈
              </span>
            </div>
          </div>

          {/* Celebration Message */}
          <div className="max-w-2xl mx-auto">
            <p className="text-gray-600 text-xl font-medium leading-relaxed">
              Fantastic! Your perfect selection is ready and waiting for you.
              Don&apos;t miss out on this amazing opportunity!
            </p>
          </div>

          {/* Decorative Elements */}
          <div className="flex justify-center space-x-8 py-4">
            <div className="flex flex-col items-center space-y-2">
              <div className="w-4 h-4 rounded-full bg-yellow-400 animate-ping"></div>
              <div className="w-2 h-2 rounded-full bg-yellow-300"></div>
            </div>
            <div className="flex flex-col items-center space-y-2">
              <div
                className="w-4 h-4 rounded-full bg-pink-400 animate-ping"
                style={{ animationDelay: "0.5s" }}
              ></div>
              <div className="w-2 h-2 rounded-full bg-pink-300"></div>
            </div>
            <div className="flex flex-col items-center space-y-2">
              <div
                className="w-4 h-4 rounded-full bg-blue-400 animate-ping"
                style={{ animationDelay: "1s" }}
              ></div>
              <div className="w-2 h-2 rounded-full bg-blue-300"></div>
            </div>
            <div className="flex flex-col items-center space-y-2">
              <div
                className="w-4 h-4 rounded-full animate-ping"
                style={{
                  backgroundColor: "oklch(44.79% 0.108 151.33)",
                  animationDelay: "1.5s",
                }}
              ></div>
              <div
                className="w-2 h-2 rounded-full"
                style={{ backgroundColor: "oklch(44.79% 0.108 151.33)" }}
              ></div>
            </div>
          </div>

          <div className="w-full flex items-center justify-between">
            <Button
              onClick={handleBackClick}
              type="button"
              variant={"outline"}
              className="py-6 rounded-full px-4 font-semibold cursor-pointer"
            >
              <span>
                <ArrowLeft className="h-4 w-4" />
              </span>
              Back to Dates
            </Button>

            <Button
              type="button"
              onClick={handleNext}
              className="py-6 rounded-full px-4 font-semibold cursor-pointer"
            >
              Proceed to Details
              <span>
                <ArrowRight className="h-4 w-4" />
              </span>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default SuccessAvailabilityCard;

const ConfettiPieces = ({ showConfetti }: { showConfetti: boolean }) => {
  const pieces = useMemo(() => {
    const colors = [
      "oklch(44.79% 0.108 151.33)",
      "#ff6b6b",
      "#4ecdc4",
      "#45b7d1",
      "#feca57",
      "#ff9ff3",
      "#96ceb4",
      "#a78bfa",
    ];
    return Array.from({ length: 40 }).map((_, i) => ({
      id: i,
      left: `${Math.random() * 100}%`,
      backgroundColor: colors[Math.floor(Math.random() * colors.length)],
      animationDelay: `${Math.random() * 2.5}s`,
      animationDuration: `${3 + Math.random() * 2}s`,
      borderRadius: Math.random() > 0.5 ? "50%" : "0%",
    }));
  }, []);

  return (
    <>
      {pieces.map((p) => (
        <div
          key={p.id}
          className={`absolute w-4 h-4 opacity-0 ${showConfetti ? "animate-confetti" : ""}`}
          style={p}
        />
      ))}
    </>
  );
};
