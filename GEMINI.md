# Project: Silent Palms Villa

This is a [Next.js](https://nextjs.org/) project for a villa rental website called "Silent Palms Villa".

## Project Overview

The project is a Next.js application built with TypeScript. It appears to be a website for a villa rental, likely featuring accommodation listings, a booking/reservation system, and a gallery.

### Key Technologies:

*   **Framework:** Next.js (App Router)
*   **Language:** TypeScript
*   **Styling:** Tailwind CSS
*   **UI Components:** Radix U<PERSON>, shadcn/ui
*   **State Management:** Zustand
*   **Forms:** React Hook Form with Zod for validation
*   **Images:** Cloudinary for image hosting.
*   **Linting:** ESLint
*   **Package Manager:** bun

## Building and Running

To get the development server running:

```bash
bun dev
```

To create a production build:

```bash
bun build
```

To start a production server:

```bash
bun start
```

To lint the code:

```bash
bun lint
```

## Development Conventions

*   **Coding Style:**
    *   The project uses a functional programming style with `const` for function declarations.
    *   Indentation is 2 spaces.
    *   Interfaces are prefixed with `I` (e.g., `IUserService`).
    *   Private class members are prefixed with an underscore (`_`).
    *   Strict equality (`===` and `!==`) is enforced.
*   **File Structure:**
    *   The main application code is in the `src` directory.
    *   Components are located in `src/components`.
    *   The Next.js app router is used, with pages in `src/app/(app)`.
    *   The project uses path aliases `@/*` for `./src/*`.
*   **Dependencies:**
    *   `bun` is the package manager.
    *   Avoid introducing new external dependencies unless absolutely necessary.

## Future Development

*   A `changes` folder should be created at the root of the project for any new or modified code.
*   All changes should be documented in `CHANGELOG.md`.
*   A `PLAN.md` file will be used to document the plan for any changes.
